{"name": "my-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.7", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "lucide-react": "^0.488.0", "marked": "^15.0.8", "next": "^15.3.0", "next-themes": "^0.4.6", "openai": "^4.94.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@types/marked": "^5.0.2", "@types/node": "^20.17.31", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "autoprefixer": "^10.4.21", "eslint": "9.24.0", "eslint-config-next": "15.3.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}