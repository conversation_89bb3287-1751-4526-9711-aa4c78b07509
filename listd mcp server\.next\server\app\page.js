(()=>{var e={};e.id=974,e.ids=[974],e.modules={195:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1137:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var s=t(7413),a=t(6797),n=t.n(a);t(1135);var o=t(3701),i=t(5986),l=t(8974);let d={title:"GooSearch - AI 增强的谷歌搜索",description:"使用 AI 技术增强的谷歌搜索引擎，提供更精准的搜索结果分析"};function c({children:e}){return(0,s.jsxs)("html",{lang:"zh-CN",suppressHydrationWarning:!0,children:[(0,s.jsx)("head",{}),(0,s.jsx)("body",{className:function(...e){return(0,l.QP)((0,i.$)(e))}("min-h-screen bg-background font-sans antialiased",n().variable),children:(0,s.jsx)(o.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e})})]})}},1204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\工作\\\\Mik\\\\deep\\\\listd mcp server\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\page.tsx","default")},2685:(e,r,t)=>{Promise.resolve().then(t.bind(t,6871))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3294:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=t(5239),a=t(8088),n=t(8170),o=t.n(n),i=t(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,1137)),"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4413)),"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\theme-provider.tsx","ThemeProvider")},3873:e=>{"use strict";e.exports=require("path")},4125:(e,r,t)=>{Promise.resolve().then(t.bind(t,3701))},4413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(7413);function a(){return(0,s.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",background:"linear-gradient(to right, #00d2ff, #3a7bd5)"},children:(0,s.jsxs)("div",{style:{textAlign:"center",color:"white",maxWidth:"600px",padding:"2rem",backgroundColor:"rgba(255, 255, 255, 0.1)",borderRadius:"1rem",backdropFilter:"blur(10px)",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},children:[(0,s.jsx)("h1",{style:{fontSize:"2.5rem",marginBottom:"1rem"},children:"404 - 页面未找到"}),(0,s.jsx)("p",{style:{fontSize:"1.2rem",marginBottom:"2rem"},children:"抱歉，您请求的页面不存在。"}),(0,s.jsx)("a",{href:"/",style:{display:"inline-block",padding:"0.75rem 1.5rem",backgroundColor:"white",color:"#3a7bd5",borderRadius:"0.5rem",textDecoration:"none",fontWeight:"bold",transition:"all 0.2s ease"},children:"返回首页"})]})})}},4493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>m});var s=t(687),a=t(3210),n=t(4780);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));o.displayName="Card";let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));m.displayName="CardFooter"},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,s.$)(e))}},5851:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},6487:()=>{},6871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>n});var s=t(687);t(3210);var a=t(218);function n({children:e,...r}){return(0,s.jsx)(a.N,{...r,children:e})}},7336:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(687),a=t(3210),n=t(1527),o=t(4493),i=t(9523),l=t(4780);let d=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));d.displayName="Input",t(1215);var c=t(1391),m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,c.TL)(`Primitive.${r}`),n=a.forwardRef((e,a)=>{let{asChild:n,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(n?t:r,{...o,ref:a})});return n.displayName=`Primitive.${r}`,{...e,[r]:n}},{}),x="horizontal",u=["horizontal","vertical"],h=a.forwardRef((e,r)=>{var t;let{decorative:a,orientation:n=x,...o}=e,i=(t=n,u.includes(t))?n:x;return(0,s.jsx)(m.div,{"data-orientation":i,...a?{role:"none"}:{"aria-orientation":"vertical"===i?i:void 0,role:"separator"},...o,ref:r})});h.displayName="Separator";let p=a.forwardRef(({className:e,orientation:r="horizontal",decorative:t=!0,...a},n)=>(0,s.jsx)(h,{ref:n,decorative:t,orientation:r,className:(0,l.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...a}));function v(){let[e,r]=(0,a.useState)(""),[t,l]=(0,a.useState)(!1),[c,m]=(0,a.useState)(""),[x,u]=(0,a.useState)(""),[h,v]=(0,a.useState)(""),[g,b]=(0,a.useState)(0),f=async(e,r,t=12e5)=>{let s=new AbortController,a=setTimeout(()=>s.abort(),t);try{console.log(`发送请求到 ${e}`);let t=await fetch(e,{...r,signal:s.signal});if(clearTimeout(a),!t.ok){let e=await t.text();throw Error(`请求失败，状态码: ${t.status}, 错误信息: ${e}`)}return t}catch(e){throw clearTimeout(a),e}},w=async r=>{if(r.preventDefault(),!e.trim())return void m("请输入搜索内容");l(!0),m(""),u("");try{let r=Date.now(),t=Math.random().toString(36).substring(2,15);console.log("发起新的搜索请求，不使用缓存");let s=await f(`/api/search?_=${r}&r=${t}`,{method:"POST",headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"},body:JSON.stringify({query:e.trim()})},12e5);console.log("开始处理响应数据...");let a=Date.now(),n=await s.json();if(console.log("JSON解析完成，耗时:",(Date.now()-a)/1e3,"秒"),n.error)m(n.error);else{if(console.log(`搜索完成，获取到结果大小:`,JSON.stringify(n).length,"字节"),!n.response)return void m("服务器返回了空结果");n.response.includes("|")&&n.response.includes("-")||console.warn("响应可能不是有效的Markdown表格格式"),setTimeout(()=>{u(n.response),console.log("结果渲染完成，总耗时:",(Date.now()-a)/1e3,"秒"),console.log("搜索结果已保存到 localStorage")},100),v(n.responseSource||"Unknown"),b(n.entityCount||0),console.log(`响应来源: ${n.responseSource}, 实体数量: ${n.entityCount}`)}}catch(e){"AbortError"===e.name?m("搜索请求超时，请尝试简化您的搜索内容或稍后再试"):m(e.message||"搜索过程中出现错误"),console.error("Search error:",e)}finally{l(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex justify-center items-center",style:{background:"linear-gradient(135deg, rgb(var(--mint-start)), rgb(var(--mint-mid)), rgb(var(--mint-end)))",backgroundSize:"200% 200%",animation:"gradient 15s ease infinite"},children:[(0,s.jsxs)("div",{className:"absolute top-0 left-0 w-full h-full overflow-hidden z-0",children:[(0,s.jsx)("div",{className:"absolute top-10 left-10 w-32 h-32 bg-white opacity-10 rounded-full"}),(0,s.jsx)("div",{className:"absolute bottom-10 right-10 w-40 h-40 bg-white opacity-10 rounded-full"}),(0,s.jsx)("div",{className:"absolute top-1/3 right-1/4 w-24 h-24 bg-white opacity-5 rounded-full"}),(0,s.jsx)("div",{className:"absolute bottom-1/4 left-1/3 w-20 h-20 bg-white opacity-5 rounded-full"})]}),(0,s.jsxs)(o.Zp,{className:"max-w-4xl w-full mx-4 my-8 glass-effect shadow-2xl overflow-hidden z-10 fade-in",children:[(0,s.jsx)("div",{className:"h-1.5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))]"}),(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsx)(o.ZB,{className:"text-5xl font-bold bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] text-transparent bg-clip-text",children:"GooSearch"}),(0,s.jsx)(o.BT,{className:"text-lg",children:"AI 增强的谷歌搜索引擎"}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)("a",{href:"/table-demo",className:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-medium border border-[rgba(var(--mint-start),0.2)] transition-all duration-300 hover:shadow-lg hover:scale-105",children:"✨ 查看表格样式演示"})})]}),(0,s.jsxs)(o.Wu,{className:"space-y-8",children:[(0,s.jsx)("form",{onSubmit:w,className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-3 md:gap-0",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(d,{type:"text",value:e,onChange:e=>r(e.target.value),placeholder:"输入您的搜索内容...",className:"w-full px-5 py-7 border-2 border-[rgba(var(--mint-mid),0.3)] rounded-lg md:rounded-r-none focus-visible:ring-[rgb(var(--mint-start))] text-center h-auto text-lg shadow-sm",autoFocus:!0}),!t&&e&&(0,s.jsx)("button",{type:"button",onClick:()=>r(""),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})]}),(0,s.jsx)(i.$,{type:"submit",disabled:t,className:"bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] text-white px-8 py-7 rounded-lg md:rounded-l-none font-medium transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px] h-auto text-lg",children:t?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"搜索中..."]}):(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),"搜索"]})})]})}),(0,s.jsx)(p,{className:"my-6 bg-[rgba(var(--mint-mid),0.1)]"}),c&&(0,s.jsx)("div",{className:"p-5 bg-[rgba(var(--accent-1),0.1)] border-l-4 border-[rgb(var(--accent-1))] text-[rgb(var(--text-primary))] rounded-lg shadow-sm fade-in",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-[rgb(var(--accent-1))]",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-base font-medium",children:c})})]})}),t&&(0,s.jsxs)("div",{className:"text-center py-10 fade-in",children:[(0,s.jsx)("p",{className:"text-[rgb(var(--mint-start))] font-medium text-xl mb-4",children:"正在搜索并分析结果"}),(0,s.jsx)("p",{className:"text-[rgb(var(--text-secondary))] text-base max-w-md mx-auto mb-4",children:"我们正在爬取和分析相关网页，这可能需要一些时间（通常1-3分钟），取决于查询的复杂性"}),(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-[rgba(var(--mint-start),0.2)] border-t-[rgb(var(--mint-start))] rounded-full animate-spin mx-auto shadow-md"}),(0,s.jsx)("div",{className:"text-[rgb(var(--mint-mid))] font-medium text-lg animate-pulse",children:"请耐心等待，系统正在处理中..."})]})]}),x&&(0,s.jsxs)(o.Zp,{className:"mt-8 card-hover fade-in border border-[rgba(var(--mint-mid),0.2)] shadow-xl overflow-hidden",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] px-6 py-4 text-center relative",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-white",children:"搜索结果"}),(0,s.jsxs)("div",{className:"flex justify-center mt-2 space-x-4",children:[(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-white/20 rounded-full text-xs font-medium text-white",children:["数据来源: ","AI"===h?"AI生成":"Cache"===h?"缓存":"自动生成"]}),(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-white/20 rounded-full text-xs font-medium text-white",children:["实体数量: ",g]})]}),(0,s.jsx)("button",{onClick:()=>{r(""),u(""),v(""),b(0),m(""),console.log("搜索结果已清除")},className:"absolute right-4 top-4 text-white/70 hover:text-white transition-colors",title:"清除结果",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})]}),(0,s.jsxs)(o.Wu,{className:"p-6 md:p-8",children:[(0,s.jsx)("div",{className:`table-container ${x.includes("|")&&x.includes("-")?"table-success":""}`,children:(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:n.xI.parse(x)},className:"prose max-w-none prose-headings:text-center prose-headings:text-[rgb(var(--text-primary))] prose-a:text-[rgb(var(--mint-start))] prose-strong:text-[rgb(var(--mint-start))] enhanced-table"})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:x.includes("|")&&x.includes("-")?(0,s.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--mint-start),0.2)] shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 animate-pulse",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"✨ 表格已成功渲染"]}):(0,s.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--accent-2),0.1)] to-[rgba(var(--accent-1),0.1)] text-[rgb(var(--accent-2))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--accent-2),0.2)] shadow-lg",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),"\uD83D\uDCC4 文本格式内容"]})}),(0,s.jsxs)("div",{className:"mt-8 p-6 bg-[rgba(var(--accent-2),0.05)] border border-[rgba(var(--accent-2),0.2)] rounded-xl",children:[(0,s.jsx)("h3",{className:"text-[rgb(var(--text-primary))] font-semibold mb-4 text-center",children:"原始响应"}),(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-sm bg-white p-5 rounded-lg border border-[rgba(var(--mint-mid),0.2)] overflow-auto max-h-96 text-[rgb(var(--text-secondary))] text-left",children:x})]})]})]}),!t&&!c&&!x&&(0,s.jsxs)("div",{className:"text-center py-16 fade-in",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold mb-5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] bg-clip-text text-transparent",children:"欢迎使用智能搜索"}),(0,s.jsx)("p",{className:"text-[rgb(var(--text-secondary))] text-lg max-w-md mx-auto",children:"输入您的搜索内容，让 AI 为您提供更智能的搜索结果分析"})]})]}),(0,s.jsx)(o.wL,{className:"border-t border-[rgba(var(--mint-mid),0.1)] bg-[rgba(var(--mint-start),0.03)] p-6 justify-center",children:(0,s.jsx)("p",{className:"text-[rgb(var(--text-secondary))] text-sm",children:"使用 AI 增强的搜索结果分析 | Powered by Next.js"})})]})]})}p.displayName=h.displayName},8335:()=>{},8356:(e,r,t)=>{Promise.resolve().then(t.bind(t,7336))},8500:(e,r,t)=>{Promise.resolve().then(t.bind(t,1204))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(687),a=t(3210),n=t(1391),o=t(4224),i=t(4780);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},d)=>{let c=a?n.DX:"button";return(0,s.jsx)(c,{className:(0,i.cn)(l({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,130,433],()=>t(3294));module.exports=s})();