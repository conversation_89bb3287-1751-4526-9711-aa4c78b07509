(()=>{var e={};e.id=684,e.ids=[684],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6183:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>h,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>x});var o=t(6559),n=t(8088),i=t(7719),p=t(2190),a=t(9646),c=t(8354),u=t(3873),l=t.n(u);let d=(0,c.promisify)(a.exec);async function x(e){try{let r=e.nextUrl.searchParams.get("url")||"https://www.example.com",t=e.nextUrl.searchParams.get("query")||"分析网页内容";console.log(`直接调用 Python 脚本`),console.log(`URL: ${r}, 查询: ${t}`);let s=l().join(process.cwd(),"scripts","scrape.py");console.log(`脚本路径: ${s}`);let{stdout:o,stderr:n}=await d(`python "${s}" --url "${r}" --prompt "${t}"`,{env:{...process.env,API_KEY:process.env.API_KEY||"",OPENAI_API_KEY:process.env.API_KEY||""},maxBuffer:0xa00000});n&&n.trim()&&console.error(`Python 脚本错误输出: ${n}`);let i=o.trim().split("\n"),a="";for(let e=i.length-1;e>=0;e--){let r=i[e].trim();if(r.startsWith("{")&&r.endsWith("}")){a=r;break}}!a&&i.length>0&&(a=i[i.length-1]);try{if(!a)return p.NextResponse.json({success:!1,error:"未找到有效的 JSON 输出",stdout:o});{let e=JSON.parse(a);return p.NextResponse.json({success:!0,result:e})}}catch(e){return console.error("无法解析 Python 脚本输出的 JSON:",e.message),p.NextResponse.json({success:!1,error:"无法解析爬取结果",details:e.message,lastLine:a||"(空)",stdout:o})}}catch(e){return console.error("API 错误:",e),p.NextResponse.json({success:!1,error:e.message},{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/direct-python/route",pathname:"/api/direct-python",filename:"route",bundlePath:"app/api/direct-python/route"},resolvedPagePath:"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\direct-python\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:y}=h;function v(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},6487:()=>{},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9646:e=>{"use strict";e.exports=require("child_process")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(6183));module.exports=s})();