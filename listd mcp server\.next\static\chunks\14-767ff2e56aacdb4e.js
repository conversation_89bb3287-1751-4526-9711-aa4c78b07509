"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[14],{2085:(e,t,r)=>{r.d(t,{F:()=>o});var n=r(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,a=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let l=s(t)||s(n);return o[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,n,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(n=e(t[r]))&&(s&&(s+=" "),s+=n)}else for(n in t)t[n]&&(s&&(s+=" "),s+=n);return s}(e))&&(n&&(n+=" "),n+=t);return n}},4624:(e,t,r)=>{r.d(t,{DX:()=>i,TL:()=>o});var n=r(2115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var l=r(5155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var o;let e,i,a=(o=r,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,t){let r={...t};for(let n in t){let s=e[n],l=t[n];/^on[A-Z]/.test(n)?s&&l?r[n]=(...e)=>{l(...e),s(...e)}:s&&(r[n]=s):"style"===n?r[n]={...s,...l}:"className"===n&&(r[n]=[s,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(c.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}(t,a):a),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...o}=e,i=n.Children.toArray(s),a=i.find(c);if(a){let e=a.props.children,s=i.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,l.jsx)(t,{...o,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var i=o("Slot"),a=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},8947:(e,t,r)=>{function n(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}r.d(t,{xI:()=>eg});let s=n(),l={exec:()=>null};function o(e,t=""){let r="string"==typeof e?e:e.source,n={replace:(e,t)=>{let s="string"==typeof t?t:t.source;return s=s.replace(i.caret,"$1"),r=r.replace(e,s),n},getRegex:()=>new RegExp(r,t)};return n}let i={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},a=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,c=/(?:[*+-]|\d{1,9}[.)])/,p=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,u=o(p).replace(/bull/g,c).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),d=o(p).replace(/bull/g,c).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),h=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,g=/(?!\s*\])(?:\\.|[^\[\]\\])+/,m=o(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",g).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),f=o(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,c).getRegex(),k="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",b=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,x=o("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",b).replace("tag",k).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),w=o(h).replace("hr",a).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex(),y={blockquote:o(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",w).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:m,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:a,html:x,lheading:u,list:f,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:w,table:l,text:/^[^\n]+/},v=o("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",a).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex(),z={...y,lheading:d,table:v,paragraph:o(h).replace("hr",a).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",v).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k).getRegex()},$={...y,html:o("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",b).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:l,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:o(h).replace("hr",a).replace("heading"," *#{1,6} *[^\n]").replace("lheading",u).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},R=/^( {2,}|\\)\n(?!\s*$)/,S=/[\p{P}\p{S}]/u,T=/[\s\p{P}\p{S}]/u,A=/[^\s\p{P}\p{S}]/u,_=o(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,T).getRegex(),P=/(?!~)[\p{P}\p{S}]/u,C=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,I=o(C,"u").replace(/punct/g,S).getRegex(),E=o(C,"u").replace(/punct/g,P).getRegex(),j="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",L=o(j,"gu").replace(/notPunctSpace/g,A).replace(/punctSpace/g,T).replace(/punct/g,S).getRegex(),q=o(j,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,P).getRegex(),B=o("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,A).replace(/punctSpace/g,T).replace(/punct/g,S).getRegex(),N=o(/\\(punct)/,"gu").replace(/punct/g,S).getRegex(),M=o(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Z=o(b).replace("(?:--\x3e|$)","--\x3e").getRegex(),O=o("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Z).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),G=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,D=o(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",G).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Q=o(/^!?\[(label)\]\[(ref)\]/).replace("label",G).replace("ref",g).getRegex(),H=o(/^!?\[(ref)\](?:\[\])?/).replace("ref",g).getRegex(),W=o("reflink|nolink(?!\\()","g").replace("reflink",Q).replace("nolink",H).getRegex(),F={_backpedal:l,anyPunctuation:N,autolink:M,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:R,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:l,emStrongLDelim:I,emStrongRDelimAst:L,emStrongRDelimUnd:B,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:D,nolink:H,punctuation:_,reflink:Q,reflinkSearch:W,tag:O,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:l},V={...F,link:o(/^!?\[(label)\]\((.*?)\)/).replace("label",G).getRegex(),reflink:o(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",G).getRegex()},X={...F,emStrongRDelimAst:q,emStrongLDelim:E,url:o(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},U={...X,br:o(R).replace("{2,}","*").getRegex(),text:o(X.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},J={normal:y,gfm:z,pedantic:$},K={normal:F,gfm:X,breaks:U,pedantic:V},Y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ee=e=>Y[e];function et(e,t){if(t){if(i.escapeTest.test(e))return e.replace(i.escapeReplace,ee)}else if(i.escapeTestNoEncode.test(e))return e.replace(i.escapeReplaceNoEncode,ee);return e}function er(e){try{e=encodeURI(e).replace(i.percentDecode,"%")}catch{return null}return e}function en(e,t){let r=e.replace(i.findPipe,(e,t,r)=>{let n=!1,s=t;for(;--s>=0&&"\\"===r[s];)n=!n;return n?"|":" |"}).split(i.splitPipe),n=0;if(r[0].trim()||r.shift(),r.length>0&&!r.at(-1)?.trim()&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;n<r.length;n++)r[n]=r[n].trim().replace(i.slashPipe,"|");return r}function es(e,t,r){let n=e.length;if(0===n)return"";let s=0;for(;s<n;)if(e.charAt(n-s-1)===t)s++;else break;return e.slice(0,n-s)}function el(e,t,r,n,s){let l=t.href,o=t.title||null,i=e[1].replace(s.other.outputLinkReplace,"$1");if("!"!==e[0].charAt(0)){n.state.inLink=!0;let e={type:"link",raw:r,href:l,title:o,text:i,tokens:n.inlineTokens(i)};return n.state.inLink=!1,e}return{type:"image",raw:r,href:l,title:o,text:i}}class eo{options;rules;lexer;constructor(e){this.options=e||s}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:es(e,"\n")}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let e=t[0],r=function(e,t,r){let n=e.match(r.other.indentCodeCompensation);if(null===n)return t;let s=n[1];return t.split("\n").map(e=>{let t=e.match(r.other.beginningSpace);if(null===t)return e;let[n]=t;return n.length>=s.length?e.slice(s.length):e}).join("\n")}(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){let t=es(e,"#");this.options.pedantic?e=t.trim():(!t||this.rules.other.endingSpaceChar.test(t))&&(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:es(t[0],"\n")}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let e=es(t[0],"\n").split("\n"),r="",n="",s=[];for(;e.length>0;){let t,l=!1,o=[];for(t=0;t<e.length;t++)if(this.rules.other.blockquoteStart.test(e[t]))o.push(e[t]),l=!0;else if(l)break;else o.push(e[t]);e=e.slice(t);let i=o.join("\n"),a=i.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${i}`:i,n=n?`${n}
${a}`:a;let c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(a,s,!0),this.lexer.state.top=c,0===e.length)break;let p=s.at(-1);if(p?.type==="code")break;if(p?.type==="blockquote"){let t=p.raw+"\n"+e.join("\n"),l=this.blockquote(t);s[s.length-1]=l,r=r.substring(0,r.length-p.raw.length)+l.raw,n=n.substring(0,n.length-p.text.length)+l.text;break}if(p?.type==="list"){let t=p.raw+"\n"+e.join("\n"),l=this.list(t);s[s.length-1]=l,r=r.substring(0,r.length-p.raw.length)+l.raw,n=n.substring(0,n.length-p.raw.length)+l.raw,e=t.substring(s.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:r,tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let r=t[1].trim(),n=r.length>1,s={type:"list",raw:"",ordered:n,start:n?+r.slice(0,-1):"",loose:!1,items:[]};r=n?`\\d{1,9}\\${r.slice(-1)}`:`\\${r}`,this.options.pedantic&&(r=n?r:"[*+-]");let l=this.rules.other.listItemRegex(r),o=!1;for(;e;){let r,n=!1,i="",a="";if(!(t=l.exec(e))||this.rules.block.hr.test(e))break;i=t[0],e=e.substring(i.length);let c=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,e=>" ".repeat(3*e.length)),p=e.split("\n",1)[0],u=!c.trim(),d=0;if(this.options.pedantic?(d=2,a=c.trimStart()):u?d=t[1].length+1:(d=(d=t[2].search(this.rules.other.nonSpaceChar))>4?1:d,a=c.slice(d),d+=t[1].length),u&&this.rules.other.blankLine.test(p)&&(i+=p+"\n",e=e.substring(p.length+1),n=!0),!n){let t=this.rules.other.nextBulletRegex(d),r=this.rules.other.hrRegex(d),n=this.rules.other.fencesBeginRegex(d),s=this.rules.other.headingBeginRegex(d),l=this.rules.other.htmlBeginRegex(d);for(;e;){let o,h=e.split("\n",1)[0];if(p=h,o=this.options.pedantic?p=p.replace(this.rules.other.listReplaceNesting,"  "):p.replace(this.rules.other.tabCharGlobal,"    "),n.test(p)||s.test(p)||l.test(p)||t.test(p)||r.test(p))break;if(o.search(this.rules.other.nonSpaceChar)>=d||!p.trim())a+="\n"+o.slice(d);else{if(u||c.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||n.test(c)||s.test(c)||r.test(c))break;a+="\n"+p}u||p.trim()||(u=!0),i+=h+"\n",e=e.substring(h.length+1),c=o.slice(d)}}!s.loose&&(o?s.loose=!0:this.rules.other.doubleBlankLine.test(i)&&(o=!0));let h=null;this.options.gfm&&(h=this.rules.other.listIsTask.exec(a))&&(r="[ ] "!==h[0],a=a.replace(this.rules.other.listReplaceTask,"")),s.items.push({type:"list_item",raw:i,task:!!h,checked:r,loose:!1,text:a,tokens:[]}),s.raw+=i}let i=s.items.at(-1);if(!i)return;i.raw=i.raw.trimEnd(),i.text=i.text.trimEnd(),s.raw=s.raw.trimEnd();for(let e=0;e<s.items.length;e++)if(this.lexer.state.top=!1,s.items[e].tokens=this.lexer.blockTokens(s.items[e].text,[]),!s.loose){let t=s.items[e].tokens.filter(e=>"space"===e.type);s.loose=t.length>0&&t.some(e=>this.rules.other.anyLine.test(e.raw))}if(s.loose)for(let e=0;e<s.items.length;e++)s.items[e].loose=!0;return s}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",n=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:r,title:n}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let r=en(t[1]),n=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],l={type:"table",raw:t[0],header:[],align:[],rows:[]};if(r.length===n.length){for(let e of n)this.rules.other.tableAlignRight.test(e)?l.align.push("right"):this.rules.other.tableAlignCenter.test(e)?l.align.push("center"):this.rules.other.tableAlignLeft.test(e)?l.align.push("left"):l.align.push(null);for(let e=0;e<r.length;e++)l.header.push({text:r[e],tokens:this.lexer.inline(r[e]),header:!0,align:l.align[e]});for(let e of s)l.rows.push(en(e,l.header.length).map((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:l.align[t]})));return l}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e))return;let t=es(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{let e=function(e,t){if(-1===e.indexOf(")"))return -1;let r=0;for(let n=0;n<e.length;n++)if("\\"===e[n])n++;else if("("===e[n])r++;else if(e[n]===t[1]&&--r<0)return n;return -1}(t[2],"()");if(e>-1){let r=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,r).trim(),t[3]=""}}let r=t[2],n="";if(this.options.pedantic){let e=this.rules.other.pedanticHrefTitle.exec(r);e&&(r=e[1],n=e[3])}else n=t[3]?t[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(r=this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)?r.slice(1):r.slice(1,-1)),el(t,{href:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r,title:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n},t[0],this.lexer,this.rules)}}reflink(e,t){let r;if((r=this.rules.inline.reflink.exec(e))||(r=this.rules.inline.nolink.exec(e))){let e=t[(r[2]||r[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!e){let e=r[0].charAt(0);return{type:"text",raw:e,text:e}}return el(r,e,r[0],this.lexer,this.rules)}}emStrong(e,t,r=""){let n=this.rules.inline.emStrongLDelim.exec(e);if(!(!n||n[3]&&r.match(this.rules.other.unicodeAlphaNumeric))&&(!(n[1]||n[2])||!r||this.rules.inline.punctuation.exec(r))){let r=[...n[0]].length-1,s,l,o=r,i=0,a="*"===n[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(a.lastIndex=0,t=t.slice(-1*e.length+r);null!=(n=a.exec(t));){if(!(s=n[1]||n[2]||n[3]||n[4]||n[5]||n[6]))continue;if(l=[...s].length,n[3]||n[4]){o+=l;continue}if((n[5]||n[6])&&r%3&&!((r+l)%3)){i+=l;continue}if((o-=l)>0)continue;l=Math.min(l,l+o+i);let t=[...n[0]][0].length,a=e.slice(0,r+n.index+t+l);if(Math.min(r,l)%2){let e=a.slice(1,-1);return{type:"em",raw:a,text:e,tokens:this.lexer.inlineTokens(e)}}let c=a.slice(2,-2);return{type:"strong",raw:a,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," "),r=this.rules.other.nonSpaceChar.test(e),n=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);return r&&n&&(e=e.substring(1,e.length-1)),{type:"codespan",raw:t[0],text:e}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let e,r;return r="@"===t[2]?"mailto:"+(e=t[1]):e=t[1],{type:"link",raw:t[0],text:e,href:r,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,r;if("@"===t[2])r="mailto:"+(e=t[0]);else{let n;do n=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(n!==t[0]);e=t[0],r="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:r,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}}class ei{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||s,this.options.tokenizer=this.options.tokenizer||new eo,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:i,block:J.normal,inline:K.normal};this.options.pedantic?(t.block=J.pedantic,t.inline=K.pedantic):this.options.gfm&&(t.block=J.gfm,this.options.breaks?t.inline=K.breaks:t.inline=K.gfm),this.tokenizer.rules=t}static get rules(){return{block:J,inline:K}}static lex(e,t){return new ei(t).lex(e)}static lexInline(e,t){return new ei(t).inlineTokens(e)}lex(e){e=e.replace(i.carriageReturn,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){let t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],r=!1){for(this.options.pedantic&&(e=e.replace(i.tabCharGlobal,"    ").replace(i.spaceLine,""));e;){let n;if(this.options.extensions?.block?.some(r=>!!(n=r.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))continue;if(n=this.tokenizer.space(e)){e=e.substring(n.raw.length);let r=t.at(-1);1===n.raw.length&&void 0!==r?r.raw+="\n":t.push(n);continue}if(n=this.tokenizer.code(e)){e=e.substring(n.raw.length);let r=t.at(-1);r?.type==="paragraph"||r?.type==="text"?(r.raw+="\n"+n.raw,r.text+="\n"+n.text,this.inlineQueue.at(-1).src=r.text):t.push(n);continue}if((n=this.tokenizer.fences(e))||(n=this.tokenizer.heading(e))||(n=this.tokenizer.hr(e))||(n=this.tokenizer.blockquote(e))||(n=this.tokenizer.list(e))||(n=this.tokenizer.html(e))){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.def(e)){e=e.substring(n.raw.length);let r=t.at(-1);r?.type==="paragraph"||r?.type==="text"?(r.raw+="\n"+n.raw,r.text+="\n"+n.raw,this.inlineQueue.at(-1).src=r.text):this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title});continue}if((n=this.tokenizer.table(e))||(n=this.tokenizer.lheading(e))){e=e.substring(n.raw.length),t.push(n);continue}let s=e;if(this.options.extensions?.startBlock){let t,r=1/0,n=e.slice(1);this.options.extensions.startBlock.forEach(e=>{"number"==typeof(t=e.call({lexer:this},n))&&t>=0&&(r=Math.min(r,t))}),r<1/0&&r>=0&&(s=e.substring(0,r+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s))){let l=t.at(-1);r&&l?.type==="paragraph"?(l.raw+="\n"+n.raw,l.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):t.push(n),r=s.length!==e.length,e=e.substring(n.raw.length);continue}if(n=this.tokenizer.text(e)){e=e.substring(n.raw.length);let r=t.at(-1);r?.type==="text"?(r.raw+="\n"+n.raw,r.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=r.text):t.push(n);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let r=e,n=null;if(this.tokens.links){let e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(n=this.tokenizer.rules.inline.reflinkSearch.exec(r));)e.includes(n[0].slice(n[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,n.index)+"["+"a".repeat(n[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(n=this.tokenizer.rules.inline.anyPunctuation.exec(r));)r=r.slice(0,n.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;null!=(n=this.tokenizer.rules.inline.blockSkip.exec(r));)r=r.slice(0,n.index)+"["+"a".repeat(n[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let s=!1,l="";for(;e;){let n;if(s||(l=""),s=!1,this.options.extensions?.inline?.some(r=>!!(n=r.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))continue;if((n=this.tokenizer.escape(e))||(n=this.tokenizer.tag(e))||(n=this.tokenizer.link(e))){e=e.substring(n.raw.length),t.push(n);continue}if(n=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(n.raw.length);let r=t.at(-1);"text"===n.type&&r?.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);continue}if((n=this.tokenizer.emStrong(e,r,l))||(n=this.tokenizer.codespan(e))||(n=this.tokenizer.br(e))||(n=this.tokenizer.del(e))||(n=this.tokenizer.autolink(e))||!this.state.inLink&&(n=this.tokenizer.url(e))){e=e.substring(n.raw.length),t.push(n);continue}let o=e;if(this.options.extensions?.startInline){let t,r=1/0,n=e.slice(1);this.options.extensions.startInline.forEach(e=>{"number"==typeof(t=e.call({lexer:this},n))&&t>=0&&(r=Math.min(r,t))}),r<1/0&&r>=0&&(o=e.substring(0,r+1))}if(n=this.tokenizer.inlineText(o)){e=e.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(l=n.raw.slice(-1)),s=!0;let r=t.at(-1);r?.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw Error(t)}}return t}}class ea{options;parser;constructor(e){this.options=e||s}space(e){return""}code({text:e,lang:t,escaped:r}){let n=(t||"").match(i.notSpaceStart)?.[0],s=e.replace(i.endingNewline,"")+"\n";return n?'<pre><code class="language-'+et(n)+'">'+(r?s:et(s,!0))+"</code></pre>\n":"<pre><code>"+(r?s:et(s,!0))+"</code></pre>\n"}blockquote({tokens:e}){let t=this.parser.parse(e);return`<blockquote>
${t}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return"<hr>\n"}list(e){let t=e.ordered,r=e.start,n="";for(let t=0;t<e.items.length;t++){let r=e.items[t];n+=this.listitem(r)}let s=t?"ol":"ul";return"<"+s+(t&&1!==r?' start="'+r+'"':"")+">\n"+n+"</"+s+">\n"}listitem(e){let t="";if(e.task){let r=this.checkbox({checked:!!e.checked});e.loose?e.tokens[0]?.type==="paragraph"?(e.tokens[0].text=r+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=r+" "+et(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):t+=r+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",r="";for(let t=0;t<e.header.length;t++)r+=this.tablecell(e.header[t]);t+=this.tablerow({text:r});let n="";for(let t=0;t<e.rows.length;t++){let s=e.rows[t];r="";for(let e=0;e<s.length;e++)r+=this.tablecell(s[e]);n+=this.tablerow({text:r})}return n&&(n=`<tbody>${n}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+n+"</table>\n"}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){let t=this.parser.parseInline(e.tokens),r=e.header?"th":"td";return(e.align?`<${r} align="${e.align}">`:`<${r}>`)+t+`</${r}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${et(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:r}){let n=this.parser.parseInline(r),s=er(e);if(null===s)return n;let l='<a href="'+(e=s)+'"';return t&&(l+=' title="'+et(t)+'"'),l+=">"+n+"</a>"}image({href:e,title:t,text:r}){let n=er(e);if(null===n)return et(r);e=n;let s=`<img src="${e}" alt="${r}"`;return t&&(s+=` title="${et(t)}"`),s+=">"}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:et(e.text)}}class ec{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class ep{options;renderer;textRenderer;constructor(e){this.options=e||s,this.options.renderer=this.options.renderer||new ea,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new ec}static parse(e,t){return new ep(t).parse(e)}static parseInline(e,t){return new ep(t).parseInline(e)}parse(e,t=!0){let r="";for(let n=0;n<e.length;n++){let s=e[n];if(this.options.extensions?.renderers?.[s.type]){let e=this.options.extensions.renderers[s.type].call({parser:this},s);if(!1!==e||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(s.type)){r+=e||"";continue}}switch(s.type){case"space":r+=this.renderer.space(s);continue;case"hr":r+=this.renderer.hr(s);continue;case"heading":r+=this.renderer.heading(s);continue;case"code":r+=this.renderer.code(s);continue;case"table":r+=this.renderer.table(s);continue;case"blockquote":r+=this.renderer.blockquote(s);continue;case"list":r+=this.renderer.list(s);continue;case"html":r+=this.renderer.html(s);continue;case"paragraph":r+=this.renderer.paragraph(s);continue;case"text":{let l=s,o=this.renderer.text(l);for(;n+1<e.length&&"text"===e[n+1].type;)l=e[++n],o+="\n"+this.renderer.text(l);t?r+=this.renderer.paragraph({type:"paragraph",raw:o,text:o,tokens:[{type:"text",raw:o,text:o,escaped:!0}]}):r+=o;continue}default:{let e='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw Error(e)}}}return r}parseInline(e,t=this.renderer){let r="";for(let n=0;n<e.length;n++){let s=e[n];if(this.options.extensions?.renderers?.[s.type]){let e=this.options.extensions.renderers[s.type].call({parser:this},s);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){r+=e||"";continue}}switch(s.type){case"escape":case"text":r+=t.text(s);break;case"html":r+=t.html(s);break;case"link":r+=t.link(s);break;case"image":r+=t.image(s);break;case"strong":r+=t.strong(s);break;case"em":r+=t.em(s);break;case"codespan":r+=t.codespan(s);break;case"br":r+=t.br(s);break;case"del":r+=t.del(s);break;default:{let e='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw Error(e)}}}return r}}class eu{options;block;constructor(e){this.options=e||s}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?ei.lex:ei.lexInline}provideParser(){return this.block?ep.parse:ep.parseInline}}class ed{defaults=n();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=ep;Renderer=ea;TextRenderer=ec;Lexer=ei;Tokenizer=eo;Hooks=eu;constructor(...e){this.use(...e)}walkTokens(e,t){let r=[];for(let n of e)switch(r=r.concat(t.call(this,n)),n.type){case"table":for(let e of n.header)r=r.concat(this.walkTokens(e.tokens,t));for(let e of n.rows)for(let n of e)r=r.concat(this.walkTokens(n.tokens,t));break;case"list":r=r.concat(this.walkTokens(n.items,t));break;default:{let e=n;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach(n=>{let s=e[n].flat(1/0);r=r.concat(this.walkTokens(s,t))}):e.tokens&&(r=r.concat(this.walkTokens(e.tokens,t)))}}return r}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(e=>{let r={...e};if(r.async=this.defaults.async||r.async||!1,e.extensions&&(e.extensions.forEach(e=>{if(!e.name)throw Error("extension name required");if("renderer"in e){let r=t.renderers[e.name];r?t.renderers[e.name]=function(...t){let n=e.renderer.apply(this,t);return!1===n&&(n=r.apply(this,t)),n}:t.renderers[e.name]=e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw Error("extension level must be 'block' or 'inline'");let r=t[e.level];r?r.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)}),r.extensions=t),e.renderer){let t=this.defaults.renderer||new ea(this.defaults);for(let r in e.renderer){if(!(r in t))throw Error(`renderer '${r}' does not exist`);if(["options","parser"].includes(r))continue;let n=e.renderer[r],s=t[r];t[r]=(...e)=>{let r=n.apply(t,e);return!1===r&&(r=s.apply(t,e)),r||""}}r.renderer=t}if(e.tokenizer){let t=this.defaults.tokenizer||new eo(this.defaults);for(let r in e.tokenizer){if(!(r in t))throw Error(`tokenizer '${r}' does not exist`);if(["options","rules","lexer"].includes(r))continue;let n=e.tokenizer[r],s=t[r];t[r]=(...e)=>{let r=n.apply(t,e);return!1===r&&(r=s.apply(t,e)),r}}r.tokenizer=t}if(e.hooks){let t=this.defaults.hooks||new eu;for(let r in e.hooks){if(!(r in t))throw Error(`hook '${r}' does not exist`);if(["options","block"].includes(r))continue;let n=e.hooks[r],s=t[r];eu.passThroughHooks.has(r)?t[r]=e=>{if(this.defaults.async)return Promise.resolve(n.call(t,e)).then(e=>s.call(t,e));let r=n.call(t,e);return s.call(t,r)}:t[r]=(...e)=>{let r=n.apply(t,e);return!1===r&&(r=s.apply(t,e)),r}}r.hooks=t}if(e.walkTokens){let t=this.defaults.walkTokens,n=e.walkTokens;r.walkTokens=function(e){let r=[];return r.push(n.call(this,e)),t&&(r=r.concat(t.call(this,e))),r}}this.defaults={...this.defaults,...r}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return ei.lex(e,t??this.defaults)}parser(e,t){return ep.parse(e,t??this.defaults)}parseMarkdown(e){return(t,r)=>{let n={...r},s={...this.defaults,...n},l=this.onError(!!s.silent,!!s.async);if(!0===this.defaults.async&&!1===n.async)return l(Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return l(Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return l(Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=e);let o=s.hooks?s.hooks.provideLexer():e?ei.lex:ei.lexInline,i=s.hooks?s.hooks.provideParser():e?ep.parse:ep.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(e=>o(e,s)).then(e=>s.hooks?s.hooks.processAllTokens(e):e).then(e=>s.walkTokens?Promise.all(this.walkTokens(e,s.walkTokens)).then(()=>e):e).then(e=>i(e,s)).then(e=>s.hooks?s.hooks.postprocess(e):e).catch(l);try{s.hooks&&(t=s.hooks.preprocess(t));let e=o(t,s);s.hooks&&(e=s.hooks.processAllTokens(e)),s.walkTokens&&this.walkTokens(e,s.walkTokens);let r=i(e,s);return s.hooks&&(r=s.hooks.postprocess(r)),r}catch(e){return l(e)}}}onError(e,t){return r=>{if(r.message+="\nPlease report this to https://github.com/markedjs/marked.",e){let e="<p>An error occurred:</p><pre>"+et(r.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(r);throw r}}}let eh=new ed;function eg(e,t){return eh.parse(e,t)}eg.options=eg.setOptions=function(e){return eh.setOptions(e),eg.defaults=eh.defaults,s=eg.defaults,eg},eg.getDefaults=n,eg.defaults=s,eg.use=function(...e){return eh.use(...e),eg.defaults=eh.defaults,s=eg.defaults,eg},eg.walkTokens=function(e,t){return eh.walkTokens(e,t)},eg.parseInline=eh.parseInline,eg.Parser=ep,eg.parser=ep.parse,eg.Renderer=ea,eg.TextRenderer=ec,eg.Lexer=ei,eg.lexer=ei.lex,eg.Tokenizer=eo,eg.Hooks=eu,eg.parse=eg,eg.options,eg.setOptions,eg.use,eg.walkTokens,eg.parseInline,ep.parse,ei.lex},9688:(e,t,r)=>{r.d(t,{QP:()=>ec});let n=e=>{let t=i(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),s(r,t)||o(e)},getConflictingClassGroupIds:(e,t)=>{let s=r[e]||[];return t&&n[e]?[...s,...n[e]]:s}}},s=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),l=n?s(e.slice(1),n):void 0;if(l)return l;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},l=/^\[(.+)\]$/,o=e=>{if(l.test(e)){let t=l.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},i=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)a(r[e],n,e,t);return n},a=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e)return p(e)?void a(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,s])=>{a(s,c(t,e),r,n)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},p=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,s=(s,l)=>{r.set(s,l),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(s(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):s(e,t)}}},d=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,s=0,l=0;for(let o=0;o<e.length;o++){let i=e[o];if(0===n&&0===s){if(":"===i){r.push(e.slice(l,o)),l=o+1;continue}if("/"===i){t=o;continue}}"["===i?n++:"]"===i?n--:"("===i?s++:")"===i&&s--}let o=0===r.length?e:e.substring(l),i=h(o);return{modifiers:r,hasImportantModifier:i!==o,baseClassName:i,maybePostfixModifierPosition:t&&t>l?t-l:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,g=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},m=e=>({cache:u(e.cacheSize),parseClassName:d(e),sortModifiers:g(e),...n(e)}),f=/\s+/,k=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:s,sortModifiers:l}=t,o=[],i=e.trim().split(f),a="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{isExternal:c,modifiers:p,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:h}=r(t);if(c){a=t+(a.length>0?" "+a:a);continue}let g=!!h,m=n(g?d.substring(0,h):d);if(!m){if(!g||!(m=n(d))){a=t+(a.length>0?" "+a:a);continue}g=!1}let f=l(p).join(":"),k=u?f+"!":f,b=k+m;if(o.includes(b))continue;o.push(b);let x=s(m,g);for(let e=0;e<x.length;++e){let t=x[e];o.push(k+t)}a=t+(a.length>0?" "+a:a)}return a};function b(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=x(e))&&(n&&(n+=" "),n+=t);return n}let x=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=x(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},y=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,v=/^\((?:(\w[\w-]*):)?(.+)\)$/i,z=/^\d+\/\d+$/,$=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,T=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,A=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,_=e=>z.test(e),P=e=>!!e&&!Number.isNaN(Number(e)),C=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&P(e.slice(0,-1)),E=e=>$.test(e),j=()=>!0,L=e=>R.test(e)&&!S.test(e),q=()=>!1,B=e=>T.test(e),N=e=>A.test(e),M=e=>!O(e)&&!F(e),Z=e=>ee(e,es,q),O=e=>y.test(e),G=e=>ee(e,el,L),D=e=>ee(e,eo,P),Q=e=>ee(e,er,q),H=e=>ee(e,en,N),W=e=>ee(e,ea,B),F=e=>v.test(e),V=e=>et(e,el),X=e=>et(e,ei),U=e=>et(e,er),J=e=>et(e,es),K=e=>et(e,en),Y=e=>et(e,ea,!0),ee=(e,t,r)=>{let n=y.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=v.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,es=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,eo=e=>"number"===e,ei=e=>"family-name"===e,ea=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...t){let r,n,s,l=function(i){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,s=r.cache.set,l=o,o(i)};function o(e){let t=n(e);if(t)return t;let l=k(e,r);return s(e,l),l}return function(){return l(b.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),s=w("tracking"),l=w("leading"),o=w("breakpoint"),i=w("container"),a=w("spacing"),c=w("radius"),p=w("shadow"),u=w("inset-shadow"),d=w("text-shadow"),h=w("drop-shadow"),g=w("blur"),m=w("perspective"),f=w("aspect"),k=w("ease"),b=w("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...y(),F,O],z=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],R=()=>[F,O,a],S=()=>[_,"full","auto",...R()],T=()=>[C,"none","subgrid",F,O],A=()=>["auto",{span:["full",C,F,O]},C,F,O],L=()=>[C,"auto",F,O],q=()=>["auto","min","max","fr",F,O],B=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],N=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...R()],et=()=>[_,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...R()],er=()=>[e,F,O],en=()=>[...y(),U,Q,{position:[F,O]}],es=()=>["no-repeat",{repeat:["","x","y","space","round"]}],el=()=>["auto","cover","contain",J,Z,{size:[F,O]}],eo=()=>[I,V,G],ei=()=>["","none","full",c,F,O],ea=()=>["",P,V,G],ec=()=>["solid","dashed","dotted","double"],ep=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[P,I,U,Q],ed=()=>["","none",g,F,O],eh=()=>["none",P,F,O],eg=()=>["none",P,F,O],em=()=>[P,F,O],ef=()=>[_,"full",...R()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[E],breakpoint:[E],color:[j],container:[E],"drop-shadow":[E],ease:["in","out","in-out"],font:[M],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[E],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[E],shadow:[E],spacing:["px",P],text:[E],"text-shadow":[E],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",_,O,F,f]}],container:["container"],columns:[{columns:[P,O,F,i]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:z()}],"overflow-x":[{"overflow-x":z()}],"overflow-y":[{"overflow-y":z()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[C,"auto",F,O]}],basis:[{basis:[_,"full","auto",i,...R()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[P,_,"auto","initial","none",O]}],grow:[{grow:["",P,F,O]}],shrink:[{shrink:["",P,F,O]}],order:[{order:[C,"first","last","none",F,O]}],"grid-cols":[{"grid-cols":T()}],"col-start-end":[{col:A()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":T()}],"row-start-end":[{row:A()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":q()}],"auto-rows":[{"auto-rows":q()}],gap:[{gap:R()}],"gap-x":[{"gap-x":R()}],"gap-y":[{"gap-y":R()}],"justify-content":[{justify:[...B(),"normal"]}],"justify-items":[{"justify-items":[...N(),"normal"]}],"justify-self":[{"justify-self":["auto",...N()]}],"align-content":[{content:["normal",...B()]}],"align-items":[{items:[...N(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...N(),{baseline:["","last"]}]}],"place-content":[{"place-content":B()}],"place-items":[{"place-items":[...N(),"baseline"]}],"place-self":[{"place-self":["auto",...N()]}],p:[{p:R()}],px:[{px:R()}],py:[{py:R()}],ps:[{ps:R()}],pe:[{pe:R()}],pt:[{pt:R()}],pr:[{pr:R()}],pb:[{pb:R()}],pl:[{pl:R()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":R()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":R()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[i,"screen",...et()]}],"min-w":[{"min-w":[i,"screen","none",...et()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[o]},...et()]}],h:[{h:["screen",...et()]}],"min-h":[{"min-h":["screen","none",...et()]}],"max-h":[{"max-h":["screen",...et()]}],"font-size":[{text:["base",r,V,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,F,D]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,O]}],"font-family":[{font:[X,O,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,F,O]}],"line-clamp":[{"line-clamp":[P,"none",F,D]}],leading:[{leading:[l,...R()]}],"list-image":[{"list-image":["none",F,O]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",F,O]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[P,"from-font","auto",F,G]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[P,"auto",F,O]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F,O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F,O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:es()}],"bg-size":[{bg:el()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},C,F,O],radial:["",F,O],conic:[C,F,O]},K,H]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:eo()}],"gradient-via-pos":[{via:eo()}],"gradient-to-pos":[{to:eo()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:ea()}],"border-w-x":[{"border-x":ea()}],"border-w-y":[{"border-y":ea()}],"border-w-s":[{"border-s":ea()}],"border-w-e":[{"border-e":ea()}],"border-w-t":[{"border-t":ea()}],"border-w-r":[{"border-r":ea()}],"border-w-b":[{"border-b":ea()}],"border-w-l":[{"border-l":ea()}],"divide-x":[{"divide-x":ea()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ea()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[P,F,O]}],"outline-w":[{outline:["",P,V,G]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",p,Y,W]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",u,Y,W]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:ea()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[P,G]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":ea()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",d,Y,W]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[P,F,O]}],"mix-blend":[{"mix-blend":[...ep(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ep()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[P]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[F,O]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[P]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:es()}],"mask-size":[{mask:el()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",F,O]}],filter:[{filter:["","none",F,O]}],blur:[{blur:ed()}],brightness:[{brightness:[P,F,O]}],contrast:[{contrast:[P,F,O]}],"drop-shadow":[{"drop-shadow":["","none",h,Y,W]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",P,F,O]}],"hue-rotate":[{"hue-rotate":[P,F,O]}],invert:[{invert:["",P,F,O]}],saturate:[{saturate:[P,F,O]}],sepia:[{sepia:["",P,F,O]}],"backdrop-filter":[{"backdrop-filter":["","none",F,O]}],"backdrop-blur":[{"backdrop-blur":ed()}],"backdrop-brightness":[{"backdrop-brightness":[P,F,O]}],"backdrop-contrast":[{"backdrop-contrast":[P,F,O]}],"backdrop-grayscale":[{"backdrop-grayscale":["",P,F,O]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[P,F,O]}],"backdrop-invert":[{"backdrop-invert":["",P,F,O]}],"backdrop-opacity":[{"backdrop-opacity":[P,F,O]}],"backdrop-saturate":[{"backdrop-saturate":[P,F,O]}],"backdrop-sepia":[{"backdrop-sepia":["",P,F,O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":R()}],"border-spacing-x":[{"border-spacing-x":R()}],"border-spacing-y":[{"border-spacing-y":R()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",F,O]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[P,"initial",F,O]}],ease:[{ease:["linear","initial",k,F,O]}],delay:[{delay:[P,F,O]}],animate:[{animate:["none",b,F,O]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,F,O]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:eh()}],"rotate-x":[{"rotate-x":eh()}],"rotate-y":[{"rotate-y":eh()}],"rotate-z":[{"rotate-z":eh()}],scale:[{scale:eg()}],"scale-x":[{"scale-x":eg()}],"scale-y":[{"scale-y":eg()}],"scale-z":[{"scale-z":eg()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[F,O,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ef()}],"translate-x":[{"translate-x":ef()}],"translate-y":[{"translate-y":ef()}],"translate-z":[{"translate-z":ef()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F,O]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F,O]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[P,V,G,D]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})}}]);