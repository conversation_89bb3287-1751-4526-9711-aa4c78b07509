(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[63],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var n=t(5155),s=t(2115),a=t(4624),i=t(2085),d=t(9434);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,asChild:l=!1,...c}=e,m=l?a.DX:"button";return(0,n.jsx)(m,{className:(0,d.cn)(o({variant:s,size:i,className:t})),ref:r,...c})});l.displayName="Button"},4934:(e,r,t)=>{Promise.resolve().then(t.bind(t,5973))},5973:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(5155),s=t(2115),a=t(8947),i=t(6695),d=t(285);function o(){let[e,r]=(0,s.useState)(0),t=[{title:"企业信息表格",markdown:'## 与"科技公司"相关的实体信息\n\n| 名称 | 提供方 | 详细内容 | 数据来源 |\n| ---- | ------ | -------- | -------- |\n| 苹果公司 | Apple Inc. | 全球领先的科技公司，主要产品包括iPhone、iPad、Mac等 | [官方网站](https://www.apple.com) |\n| 微软公司 | Microsoft | 软件巨头，Windows操作系统和Office办公套件的开发商 | [官方网站](https://www.microsoft.com) |\n| 谷歌公司 | Alphabet Inc. | 搜索引擎和云服务提供商，Android系统开发者 | [官方网站](https://www.google.com) |\n| 亚马逊 | Amazon | 电商平台和云计算服务提供商AWS | [官方网站](https://www.amazon.com) |\n| 特斯拉 | Tesla Inc. | 电动汽车和清洁能源公司 | [官方网站](https://www.tesla.com) |'},{title:"产品对比表格",markdown:"## 智能手机产品对比\n\n| 产品名称 | 品牌 | 价格 | 屏幕尺寸 | 存储容量 | 评分 |\n| -------- | ---- | ---- | -------- | -------- | ---- |\n| iPhone 15 Pro | Apple | \xa58999 | 6.1英寸 | 128GB | ⭐⭐⭐⭐⭐ |\n| Galaxy S24 Ultra | Samsung | \xa59999 | 6.8英寸 | 256GB | ⭐⭐⭐⭐⭐ |\n| Pixel 8 Pro | Google | \xa56999 | 6.7英寸 | 128GB | ⭐⭐⭐⭐ |\n| 小米14 Ultra | Xiaomi | \xa55999 | 6.73英寸 | 256GB | ⭐⭐⭐⭐ |\n| OnePlus 12 | OnePlus | \xa54999 | 6.82英寸 | 256GB | ⭐⭐⭐⭐ |"},{title:"数据统计表格",markdown:"## 2024年全球市场数据\n\n| 地区 | 人口数量 | GDP总量 | 增长率 | 主要产业 |\n| ---- | -------- | ------- | ------ | -------- |\n| 中国 | 14.1亿 | $17.7万亿 | ****% | 制造业、科技、服务业 |\n| 美国 | 3.3亿 | $26.9万亿 | ****% | 科技、金融、服务业 |\n| 日本 | 1.25亿 | $4.9万亿 | +0.9% | 制造业、科技、汽车 |\n| 德国 | 8300万 | $4.3万亿 | ****% | 制造业、汽车、化工 |\n| 印度 | 14.2亿 | $3.7万亿 | ****% | IT服务、制造业、农业 |"},{title:"复杂数据表格",markdown:"## 全球顶级大学排名\n\n| 排名 | 大学名称 | 国家/地区 | 建校年份 | 学生人数 | 知名校友 | 官方网站 |\n| ---- | -------- | --------- | -------- | -------- | -------- | -------- |\n| 1 | 哈佛大学 | 美国 | 1636年 | 23,000 | 奥巴马、扎克伯格 | [harvard.edu](https://www.harvard.edu) |\n| 2 | 斯坦福大学 | 美国 | 1885年 | 17,000 | 拉里\xb7佩奇、谢尔盖\xb7布林 | [stanford.edu](https://www.stanford.edu) |\n| 3 | 麻省理工学院 | 美国 | 1861年 | 11,500 | 钱学森、贝索斯 | [mit.edu](https://www.mit.edu) |\n| 4 | 剑桥大学 | 英国 | 1209年 | 24,000 | 牛顿、霍金 | [cam.ac.uk](https://www.cam.ac.uk) |\n| 5 | 牛津大学 | 英国 | 1096年 | 26,000 | 撒切尔夫人、托尔金 | [ox.ac.uk](https://www.ox.ac.uk) |"}];return(0,n.jsx)("div",{className:"min-h-screen flex justify-center items-center",style:{background:"linear-gradient(135deg, rgb(var(--mint-start)), rgb(var(--mint-mid)), rgb(var(--mint-end)))",backgroundSize:"200% 200%",animation:"gradient 15s ease infinite"},children:(0,n.jsxs)(i.Zp,{className:"max-w-6xl w-full mx-4 my-8 glass-effect shadow-2xl overflow-hidden z-10 fade-in",children:[(0,n.jsx)("div",{className:"h-1.5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))]"}),(0,n.jsxs)(i.aR,{className:"text-center",children:[(0,n.jsx)(i.ZB,{className:"text-4xl font-bold bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] text-transparent bg-clip-text",children:"表格样式演示"}),(0,n.jsx)("p",{className:"text-lg text-[rgb(var(--text-secondary))] mt-2",children:"展示美化后的表格效果"})]}),(0,n.jsxs)(i.Wu,{className:"p-6 md:p-8",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,n.jsx)(d.$,{onClick:()=>{r(e=>(e-1+t.length)%t.length)},className:"bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-mid))] text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:scale-105",children:"← 上一个"}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h3",{className:"text-xl font-semibold text-[rgb(var(--text-primary))]",children:t[e].title}),(0,n.jsxs)("p",{className:"text-sm text-[rgb(var(--text-secondary))]",children:[e+1," / ",t.length]})]}),(0,n.jsx)(d.$,{onClick:()=>{r(e=>(e+1)%t.length)},className:"bg-gradient-to-r from-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:scale-105",children:"下一个 →"})]}),(0,n.jsx)("div",{className:"table-container table-success",children:(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:a.xI.parse(t[e].markdown)},className:"prose max-w-none prose-headings:text-center prose-headings:text-[rgb(var(--text-primary))] prose-a:text-[rgb(var(--mint-start))] prose-strong:text-[rgb(var(--mint-start))] enhanced-table"})}),(0,n.jsx)("div",{className:"mt-8 text-center",children:(0,n.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--mint-start),0.2)] shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 animate-pulse",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"✨ 表格已成功渲染"]})}),(0,n.jsxs)("div",{className:"mt-8 p-6 bg-[rgba(var(--mint-start),0.05)] border border-[rgba(var(--mint-start),0.2)] rounded-xl",children:[(0,n.jsx)("h3",{className:"text-[rgb(var(--text-primary))] font-semibold mb-4 text-center",children:"✨ 表格美化特性"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-[rgb(var(--text-secondary))]",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("p",{children:["\uD83C\uDFA8 ",(0,n.jsx)("strong",{children:"渐变表头"}),"：美丽的薄荷色渐变背景"]}),(0,n.jsxs)("p",{children:["\uD83C\uDF0A ",(0,n.jsx)("strong",{children:"悬停效果"}),"：行悬停时的平滑动画"]}),(0,n.jsxs)("p",{children:["\uD83D\uDD17 ",(0,n.jsx)("strong",{children:"链接美化"}),"：带有光泽效果的链接按钮"]}),(0,n.jsxs)("p",{children:["\uD83D\uDCF1 ",(0,n.jsx)("strong",{children:"响应式设计"}),"：完美适配移动设备"]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("p",{children:["✨ ",(0,n.jsx)("strong",{children:"加载动画"}),"：表格行依次出现效果"]}),(0,n.jsxs)("p",{children:["\uD83C\uDFAF ",(0,n.jsx)("strong",{children:"第一列强调"}),"：特殊的边框和背景"]}),(0,n.jsxs)("p",{children:["\uD83C\uDF08 ",(0,n.jsx)("strong",{children:"交替行色"}),"：提高可读性的斑马纹"]}),(0,n.jsxs)("p",{children:["\uD83D\uDCAB ",(0,n.jsx)("strong",{children:"阴影效果"}),"：现代化的立体感设计"]})]})]})]}),(0,n.jsx)("div",{className:"mt-8 text-center",children:(0,n.jsx)(d.$,{onClick:()=>window.location.href="/",className:"bg-gradient-to-r from-[rgb(var(--accent-1))] to-[rgb(var(--accent-2))] text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:scale-105",children:"\uD83C\uDFE0 返回主页"})})]})]})})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>d,wL:()=>m});var n=t(5155),s=t(2115),a=t(9434);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});i.displayName="Card";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...s})});d.displayName="CardHeader";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...s})});l.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent";let m=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...s})});m.displayName="CardFooter"},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(2596),s=t(9688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,n.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[14,441,303,358],()=>r(4934)),_N_E=e.O()}]);