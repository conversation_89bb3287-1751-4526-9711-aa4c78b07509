(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155),s=r(2115),n=r(4624),o=r(2085),l=r(9434);let i=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(i({variant:s,size:o,className:r})),ref:t,...c})});d.displayName="Button"},3388:(e,t,r)=>{Promise.resolve().then(r.bind(r,5989))},5989:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(5155),s=r(2115),n=r(8947),o=r(6695),l=r(285),i=r(9434);let d=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});d.displayName="Input",r(7650);var c=r(4624),m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=(0,c.TL)(`Primitive.${t}`),n=s.forwardRef((e,s)=>{let{asChild:n,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?r:t,{...o,ref:s})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),x="horizontal",u=["horizontal","vertical"],g=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:n=x,...o}=e,l=(r=n,u.includes(r))?n:x;return(0,a.jsx)(m.div,{"data-orientation":l,...s?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...o,ref:t})});g.displayName="Separator";let h=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:n=!0,...o}=e;return(0,a.jsx)(g,{ref:t,decorative:n,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...o})});function f(){let[e,t]=(0,s.useState)(""),[r,i]=(0,s.useState)(!1),[c,m]=(0,s.useState)(""),[x,u]=(0,s.useState)(""),[g,f]=(0,s.useState)(""),[v,b]=(0,s.useState)(0);(0,s.useEffect)(()=>{{let e=localStorage.getItem("lastQuery"),r=localStorage.getItem("lastResult"),a=localStorage.getItem("lastResponseSource"),s=localStorage.getItem("lastEntityCount");e&&t(e),r&&u(r),a&&f(a),s&&b(parseInt(s,10)||0)}},[]),(0,s.useEffect)(()=>{n.xI.use({gfm:!0,breaks:!0,tables:!0,headerIds:!1,mangle:!1})},[]);let p=async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:12e5,a=new AbortController,s=setTimeout(()=>a.abort(),r);try{console.log("发送请求到 ".concat(e));let r=await fetch(e,{...t,signal:a.signal});if(clearTimeout(s),!r.ok){let e=await r.text();throw Error("请求失败，状态码: ".concat(r.status,", 错误信息: ").concat(e))}return r}catch(e){throw clearTimeout(s),e}},w=async t=>{if(t.preventDefault(),!e.trim())return void m("请输入搜索内容");i(!0),m(""),u("");try{let t=Date.now(),r=Math.random().toString(36).substring(2,15);console.log("发起新的搜索请求，不使用缓存");let a=await p("/api/search?_=".concat(t,"&r=").concat(r),{method:"POST",headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"},body:JSON.stringify({query:e.trim()})},12e5);console.log("开始处理响应数据...");let s=Date.now(),n=await a.json();if(console.log("JSON解析完成，耗时:",(Date.now()-s)/1e3,"秒"),n.error)m(n.error);else{if(console.log("搜索完成，获取到结果大小:",JSON.stringify(n).length,"字节"),!n.response)return void m("服务器返回了空结果");n.response.includes("|")&&n.response.includes("-")||console.warn("响应可能不是有效的Markdown表格格式"),setTimeout(()=>{u(n.response),localStorage.setItem("lastQuery",e.trim()),localStorage.setItem("lastResult",n.response),localStorage.setItem("lastResponseSource",n.responseSource||"Unknown"),localStorage.setItem("lastEntityCount",String(n.entityCount||0)),console.log("结果渲染完成，总耗时:",(Date.now()-s)/1e3,"秒"),console.log("搜索结果已保存到 localStorage")},100),f(n.responseSource||"Unknown"),b(n.entityCount||0),console.log("响应来源: ".concat(n.responseSource,", 实体数量: ").concat(n.entityCount))}}catch(e){"AbortError"===e.name?m("搜索请求超时，请尝试简化您的搜索内容或稍后再试"):m(e.message||"搜索过程中出现错误"),console.error("Search error:",e)}finally{i(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex justify-center items-center",style:{background:"linear-gradient(135deg, rgb(var(--mint-start)), rgb(var(--mint-mid)), rgb(var(--mint-end)))",backgroundSize:"200% 200%",animation:"gradient 15s ease infinite"},children:[(0,a.jsxs)("div",{className:"absolute top-0 left-0 w-full h-full overflow-hidden z-0",children:[(0,a.jsx)("div",{className:"absolute top-10 left-10 w-32 h-32 bg-white opacity-10 rounded-full"}),(0,a.jsx)("div",{className:"absolute bottom-10 right-10 w-40 h-40 bg-white opacity-10 rounded-full"}),(0,a.jsx)("div",{className:"absolute top-1/3 right-1/4 w-24 h-24 bg-white opacity-5 rounded-full"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 left-1/3 w-20 h-20 bg-white opacity-5 rounded-full"})]}),(0,a.jsxs)(o.Zp,{className:"max-w-4xl w-full mx-4 my-8 glass-effect shadow-2xl overflow-hidden z-10 fade-in",children:[(0,a.jsx)("div",{className:"h-1.5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))]"}),(0,a.jsxs)(o.aR,{className:"text-center",children:[(0,a.jsx)(o.ZB,{className:"text-5xl font-bold bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] text-transparent bg-clip-text",children:"GooSearch"}),(0,a.jsx)(o.BT,{className:"text-lg",children:"AI 增强的谷歌搜索引擎"}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("a",{href:"/table-demo",className:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-medium border border-[rgba(var(--mint-start),0.2)] transition-all duration-300 hover:shadow-lg hover:scale-105",children:"✨ 查看表格样式演示"})})]}),(0,a.jsxs)(o.Wu,{className:"space-y-8",children:[(0,a.jsx)("form",{onSubmit:w,className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-3 md:gap-0",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(d,{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"输入您的搜索内容...",className:"w-full px-5 py-7 border-2 border-[rgba(var(--mint-mid),0.3)] rounded-lg md:rounded-r-none focus-visible:ring-[rgb(var(--mint-start))] text-center h-auto text-lg shadow-sm",autoFocus:!0}),!r&&e&&(0,a.jsx)("button",{type:"button",onClick:()=>t(""),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})]}),(0,a.jsx)(l.$,{type:"submit",disabled:r,className:"bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] text-white px-8 py-7 rounded-lg md:rounded-l-none font-medium transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px] h-auto text-lg",children:r?(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"搜索中..."]}):(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),"搜索"]})})]})}),(0,a.jsx)(h,{className:"my-6 bg-[rgba(var(--mint-mid),0.1)]"}),c&&(0,a.jsx)("div",{className:"p-5 bg-[rgba(var(--accent-1),0.1)] border-l-4 border-[rgb(var(--accent-1))] text-[rgb(var(--text-primary))] rounded-lg shadow-sm fade-in",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-[rgb(var(--accent-1))]",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-base font-medium",children:c})})]})}),r&&(0,a.jsxs)("div",{className:"text-center py-10 fade-in",children:[(0,a.jsx)("p",{className:"text-[rgb(var(--mint-start))] font-medium text-xl mb-4",children:"正在搜索并分析结果"}),(0,a.jsx)("p",{className:"text-[rgb(var(--text-secondary))] text-base max-w-md mx-auto mb-4",children:"我们正在爬取和分析相关网页，这可能需要一些时间（通常1-3分钟），取决于查询的复杂性"}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-[rgba(var(--mint-start),0.2)] border-t-[rgb(var(--mint-start))] rounded-full animate-spin mx-auto shadow-md"}),(0,a.jsx)("div",{className:"text-[rgb(var(--mint-mid))] font-medium text-lg animate-pulse",children:"请耐心等待，系统正在处理中..."})]})]}),x&&(0,a.jsxs)(o.Zp,{className:"mt-8 card-hover fade-in border border-[rgba(var(--mint-mid),0.2)] shadow-xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] px-6 py-4 text-center relative",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"搜索结果"}),(0,a.jsxs)("div",{className:"flex justify-center mt-2 space-x-4",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-white/20 rounded-full text-xs font-medium text-white",children:["数据来源: ","AI"===g?"AI生成":"Cache"===g?"缓存":"自动生成"]}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-white/20 rounded-full text-xs font-medium text-white",children:["实体数量: ",v]})]}),(0,a.jsx)("button",{onClick:()=>{t(""),u(""),f(""),b(0),m(""),localStorage.removeItem("lastQuery"),localStorage.removeItem("lastResult"),localStorage.removeItem("lastResponseSource"),localStorage.removeItem("lastEntityCount"),console.log("搜索结果已清除")},className:"absolute right-4 top-4 text-white/70 hover:text-white transition-colors",title:"清除结果",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})]}),(0,a.jsxs)(o.Wu,{className:"p-6 md:p-8",children:[(0,a.jsx)("div",{className:"table-container ".concat(x.includes("|")&&x.includes("-")?"table-success":""),children:(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:n.xI.parse(x)},className:"prose max-w-none prose-headings:text-center prose-headings:text-[rgb(var(--text-primary))] prose-a:text-[rgb(var(--mint-start))] prose-strong:text-[rgb(var(--mint-start))] enhanced-table"})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:x.includes("|")&&x.includes("-")?(0,a.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--mint-start),0.2)] shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 animate-pulse",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"✨ 表格已成功渲染"]}):(0,a.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--accent-2),0.1)] to-[rgba(var(--accent-1),0.1)] text-[rgb(var(--accent-2))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--accent-2),0.2)] shadow-lg",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),"\uD83D\uDCC4 文本格式内容"]})}),(0,a.jsxs)("div",{className:"mt-8 p-6 bg-[rgba(var(--accent-2),0.05)] border border-[rgba(var(--accent-2),0.2)] rounded-xl",children:[(0,a.jsx)("h3",{className:"text-[rgb(var(--text-primary))] font-semibold mb-4 text-center",children:"原始响应"}),(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm bg-white p-5 rounded-lg border border-[rgba(var(--mint-mid),0.2)] overflow-auto max-h-96 text-[rgb(var(--text-secondary))] text-left",children:x})]})]})]}),!r&&!c&&!x&&(0,a.jsxs)("div",{className:"text-center py-16 fade-in",children:[(0,a.jsx)("h3",{className:"text-3xl font-bold mb-5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] bg-clip-text text-transparent",children:"欢迎使用智能搜索"}),(0,a.jsx)("p",{className:"text-[rgb(var(--text-secondary))] text-lg max-w-md mx-auto",children:"输入您的搜索内容，让 AI 为您提供更智能的搜索结果分析"})]})]}),(0,a.jsx)(o.wL,{className:"border-t border-[rgba(var(--mint-mid),0.1)] bg-[rgba(var(--mint-start),0.03)] p-6 justify-center",children:(0,a.jsx)("p",{className:"text-[rgb(var(--text-secondary))] text-sm",children:"使用 AI 增强的搜索结果分析 | Powered by Next.js"})})]})]})}h.displayName=g.displayName},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>l,wL:()=>m});var a=r(5155),s=r(2115),n=r(9434);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});o.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});i.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})});m.displayName="CardFooter"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[14,441,303,358],()=>t(3388)),_N_E=e.O()}]);