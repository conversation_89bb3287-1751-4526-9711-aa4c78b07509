[{"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\direct-python\\route.ts": "1", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\save-data\\route.ts": "2", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\search\\route.ts": "3", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\layout.tsx": "4", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\not-found.tsx": "5", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\page.tsx": "6", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\table-demo\\page.tsx": "7", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\footer.tsx": "8", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\navbar.tsx": "9", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\SearchForm.tsx": "10", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\theme-provider.tsx": "11", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\button.tsx": "12", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\card.tsx": "13", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\dropdown-menu.tsx": "14", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\input.tsx": "15", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\separator.tsx": "16", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\theme-toggle.tsx": "17", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\lib\\googleAgentdemo.ts": "18", "D:\\工作\\Mik\\deep\\listd mcp server\\src\\lib\\utils.ts": "19"}, {"size": 2794, "mtime": 1745583519777, "results": "20", "hashOfConfig": "21"}, {"size": 9438, "mtime": 1746592056700, "results": "22", "hashOfConfig": "21"}, {"size": 5371, "mtime": 1747360362650, "results": "23", "hashOfConfig": "21"}, {"size": 1033, "mtime": 1744876254182, "results": "24", "hashOfConfig": "21"}, {"size": 1153, "mtime": 1745584706354, "results": "25", "hashOfConfig": "21"}, {"size": 18622, "mtime": 1748932475533, "results": "26", "hashOfConfig": "21"}, {"size": 8476, "mtime": 1748401985113, "results": "27", "hashOfConfig": "21"}, {"size": 472, "mtime": 1744876314192, "results": "28", "hashOfConfig": "21"}, {"size": 1165, "mtime": 1744876290761, "results": "29", "hashOfConfig": "21"}, {"size": 12492, "mtime": 1744884749225, "results": "30", "hashOfConfig": "21"}, {"size": 445, "mtime": 1745585323254, "results": "31", "hashOfConfig": "21"}, {"size": 1835, "mtime": 1744870654726, "results": "32", "hashOfConfig": "21"}, {"size": 1877, "mtime": 1744870687253, "results": "33", "hashOfConfig": "21"}, {"size": 7309, "mtime": 1744876153716, "results": "34", "hashOfConfig": "21"}, {"size": 824, "mtime": 1744870671327, "results": "35", "hashOfConfig": "21"}, {"size": 756, "mtime": 1744870700563, "results": "36", "hashOfConfig": "21"}, {"size": 1265, "mtime": 1744876106480, "results": "37", "hashOfConfig": "21"}, {"size": 37530, "mtime": 1748932594347, "results": "38", "hashOfConfig": "21"}, {"size": 167, "mtime": 1744870569314, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "iatm87", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 33, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\direct-python\\route.ts", ["97", "98"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\save-data\\route.ts", ["99", "100", "101", "102", "103", "104", "105", "106"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\search\\route.ts", ["107"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\layout.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\not-found.tsx", ["108"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\page.tsx", ["109"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\table-demo\\page.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\footer.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\navbar.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\SearchForm.tsx", ["110"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\theme-provider.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\button.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\card.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\input.tsx", ["111"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\separator.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\ui\\theme-toggle.tsx", [], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\lib\\googleAgentdemo.ts", ["112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144"], [], "D:\\工作\\Mik\\deep\\listd mcp server\\src\\lib\\utils.ts", [], [], {"ruleId": "145", "severity": 2, "message": "146", "line": 69, "column": 26, "nodeType": "147", "messageId": "148", "endLine": 69, "endColumn": 29, "suggestions": "149"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 81, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 81, "endColumn": 22, "suggestions": "150"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 25, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 25, "endColumn": 22, "suggestions": "151"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 31, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 31, "endColumn": 22, "suggestions": "152"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 122, "column": 25, "nodeType": "147", "messageId": "148", "endLine": 122, "endColumn": 28, "suggestions": "153"}, {"ruleId": "154", "severity": 2, "message": "155", "line": 155, "column": 22, "nodeType": null, "messageId": "156", "endLine": 155, "endColumn": 32}, {"ruleId": "154", "severity": 2, "message": "157", "line": 163, "column": 18, "nodeType": null, "messageId": "156", "endLine": 163, "endColumn": 27}, {"ruleId": "145", "severity": 2, "message": "146", "line": 172, "column": 61, "nodeType": "147", "messageId": "148", "endLine": 172, "endColumn": 64, "suggestions": "158"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 213, "column": 63, "nodeType": "147", "messageId": "148", "endLine": 213, "endColumn": 66, "suggestions": "159"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 263, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 263, "endColumn": 22, "suggestions": "160"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 121, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 121, "endColumn": 22, "suggestions": "161"}, {"ruleId": "162", "severity": 2, "message": "163", "line": 24, "column": 9, "nodeType": "164", "endLine": 33, "endColumn": 12}, {"ruleId": "145", "severity": 2, "message": "146", "line": 166, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 166, "endColumn": 24, "suggestions": "165"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 49, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 49, "endColumn": 24, "suggestions": "166"}, {"ruleId": "167", "severity": 2, "message": "168", "line": 5, "column": 18, "nodeType": "169", "messageId": "170", "endLine": 5, "endColumn": 28, "suggestions": "171"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 55, "column": 39, "nodeType": "147", "messageId": "148", "endLine": 55, "endColumn": 42, "suggestions": "172"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 55, "column": 86, "nodeType": "147", "messageId": "148", "endLine": 55, "endColumn": 89, "suggestions": "173"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 64, "column": 43, "nodeType": "147", "messageId": "148", "endLine": 64, "endColumn": 46, "suggestions": "174"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 66, "column": 24, "nodeType": "147", "messageId": "148", "endLine": 66, "endColumn": 27, "suggestions": "175"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 175, "column": 52, "nodeType": "147", "messageId": "148", "endLine": 175, "endColumn": 55, "suggestions": "176"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 215, "column": 23, "nodeType": "147", "messageId": "148", "endLine": 215, "endColumn": 26, "suggestions": "177"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 342, "column": 54, "nodeType": "147", "messageId": "148", "endLine": 342, "endColumn": 57, "suggestions": "178"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 368, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 368, "endColumn": 24, "suggestions": "179"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 407, "column": 35, "nodeType": "147", "messageId": "148", "endLine": 407, "endColumn": 38, "suggestions": "180"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 407, "column": 55, "nodeType": "147", "messageId": "148", "endLine": 407, "endColumn": 58, "suggestions": "181"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 407, "column": 63, "nodeType": "147", "messageId": "148", "endLine": 407, "endColumn": 66, "suggestions": "182"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 464, "column": 72, "nodeType": "147", "messageId": "148", "endLine": 464, "endColumn": 75, "suggestions": "183"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 549, "column": 27, "nodeType": "147", "messageId": "148", "endLine": 549, "endColumn": 30, "suggestions": "184"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 558, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 558, "endColumn": 24, "suggestions": "185"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 570, "column": 43, "nodeType": "147", "messageId": "148", "endLine": 570, "endColumn": 46, "suggestions": "186"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 570, "column": 74, "nodeType": "147", "messageId": "148", "endLine": 570, "endColumn": 77, "suggestions": "187"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 574, "column": 25, "nodeType": "147", "messageId": "148", "endLine": 574, "endColumn": 28, "suggestions": "188"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 613, "column": 23, "nodeType": "147", "messageId": "148", "endLine": 613, "endColumn": 26, "suggestions": "189"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 623, "column": 91, "nodeType": "147", "messageId": "148", "endLine": 623, "endColumn": 94, "suggestions": "190"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 626, "column": 19, "nodeType": "147", "messageId": "148", "endLine": 626, "endColumn": 22, "suggestions": "191"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 647, "column": 27, "nodeType": "147", "messageId": "148", "endLine": 647, "endColumn": 30, "suggestions": "192"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 663, "column": 83, "nodeType": "147", "messageId": "148", "endLine": 663, "endColumn": 86, "suggestions": "193"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 692, "column": 28, "nodeType": "147", "messageId": "148", "endLine": 692, "endColumn": 31, "suggestions": "194"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 706, "column": 30, "nodeType": "147", "messageId": "148", "endLine": 706, "endColumn": 33, "suggestions": "195"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 740, "column": 23, "nodeType": "147", "messageId": "148", "endLine": 740, "endColumn": 26, "suggestions": "196"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 757, "column": 38, "nodeType": "147", "messageId": "148", "endLine": 757, "endColumn": 41, "suggestions": "197"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 776, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 776, "endColumn": 24, "suggestions": "198"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 799, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 799, "endColumn": 24, "suggestions": "199"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 809, "column": 48, "nodeType": "147", "messageId": "148", "endLine": 809, "endColumn": 51, "suggestions": "200"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 862, "column": 29, "nodeType": "147", "messageId": "148", "endLine": 862, "endColumn": 32, "suggestions": "201"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 882, "column": 25, "nodeType": "147", "messageId": "148", "endLine": 882, "endColumn": 28, "suggestions": "202"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 883, "column": 22, "nodeType": "147", "messageId": "148", "endLine": 883, "endColumn": 25, "suggestions": "203"}, {"ruleId": "145", "severity": 2, "message": "146", "line": 977, "column": 21, "nodeType": "147", "messageId": "148", "endLine": 977, "endColumn": 24, "suggestions": "204"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["205", "206"], ["207", "208"], ["209", "210"], ["211", "212"], ["213", "214"], "@typescript-eslint/no-unused-vars", "'parseError' is defined but never used.", "unusedVar", "'readError' is defined but never used.", ["215", "216"], ["217", "218"], ["219", "220"], ["221", "222"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["223", "224"], ["225", "226"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["227"], ["228", "229"], ["230", "231"], ["232", "233"], ["234", "235"], ["236", "237"], ["238", "239"], ["240", "241"], ["242", "243"], ["244", "245"], ["246", "247"], ["248", "249"], ["250", "251"], ["252", "253"], ["254", "255"], ["256", "257"], ["258", "259"], ["260", "261"], ["262", "263"], ["264", "265"], ["266", "267"], ["268", "269"], ["270", "271"], ["272", "273"], ["274", "275"], ["276", "277"], ["278", "279"], ["280", "281"], ["282", "283"], ["284", "285"], ["286", "287"], ["288", "289"], ["290", "291"], ["292", "293"], {"messageId": "294", "fix": "295", "desc": "296"}, {"messageId": "297", "fix": "298", "desc": "299"}, {"messageId": "294", "fix": "300", "desc": "296"}, {"messageId": "297", "fix": "301", "desc": "299"}, {"messageId": "294", "fix": "302", "desc": "296"}, {"messageId": "297", "fix": "303", "desc": "299"}, {"messageId": "294", "fix": "304", "desc": "296"}, {"messageId": "297", "fix": "305", "desc": "299"}, {"messageId": "294", "fix": "306", "desc": "296"}, {"messageId": "297", "fix": "307", "desc": "299"}, {"messageId": "294", "fix": "308", "desc": "296"}, {"messageId": "297", "fix": "309", "desc": "299"}, {"messageId": "294", "fix": "310", "desc": "296"}, {"messageId": "297", "fix": "311", "desc": "299"}, {"messageId": "294", "fix": "312", "desc": "296"}, {"messageId": "297", "fix": "313", "desc": "299"}, {"messageId": "294", "fix": "314", "desc": "296"}, {"messageId": "297", "fix": "315", "desc": "299"}, {"messageId": "294", "fix": "316", "desc": "296"}, {"messageId": "297", "fix": "317", "desc": "299"}, {"messageId": "294", "fix": "318", "desc": "296"}, {"messageId": "297", "fix": "319", "desc": "299"}, {"messageId": "320", "fix": "321", "desc": "322"}, {"messageId": "294", "fix": "323", "desc": "296"}, {"messageId": "297", "fix": "324", "desc": "299"}, {"messageId": "294", "fix": "325", "desc": "296"}, {"messageId": "297", "fix": "326", "desc": "299"}, {"messageId": "294", "fix": "327", "desc": "296"}, {"messageId": "297", "fix": "328", "desc": "299"}, {"messageId": "294", "fix": "329", "desc": "296"}, {"messageId": "297", "fix": "330", "desc": "299"}, {"messageId": "294", "fix": "331", "desc": "296"}, {"messageId": "297", "fix": "332", "desc": "299"}, {"messageId": "294", "fix": "333", "desc": "296"}, {"messageId": "297", "fix": "334", "desc": "299"}, {"messageId": "294", "fix": "335", "desc": "296"}, {"messageId": "297", "fix": "336", "desc": "299"}, {"messageId": "294", "fix": "337", "desc": "296"}, {"messageId": "297", "fix": "338", "desc": "299"}, {"messageId": "294", "fix": "339", "desc": "296"}, {"messageId": "297", "fix": "340", "desc": "299"}, {"messageId": "294", "fix": "341", "desc": "296"}, {"messageId": "297", "fix": "342", "desc": "299"}, {"messageId": "294", "fix": "343", "desc": "296"}, {"messageId": "297", "fix": "344", "desc": "299"}, {"messageId": "294", "fix": "345", "desc": "296"}, {"messageId": "297", "fix": "346", "desc": "299"}, {"messageId": "294", "fix": "347", "desc": "296"}, {"messageId": "297", "fix": "348", "desc": "299"}, {"messageId": "294", "fix": "349", "desc": "296"}, {"messageId": "297", "fix": "350", "desc": "299"}, {"messageId": "294", "fix": "351", "desc": "296"}, {"messageId": "297", "fix": "352", "desc": "299"}, {"messageId": "294", "fix": "353", "desc": "296"}, {"messageId": "297", "fix": "354", "desc": "299"}, {"messageId": "294", "fix": "355", "desc": "296"}, {"messageId": "297", "fix": "356", "desc": "299"}, {"messageId": "294", "fix": "357", "desc": "296"}, {"messageId": "297", "fix": "358", "desc": "299"}, {"messageId": "294", "fix": "359", "desc": "296"}, {"messageId": "297", "fix": "360", "desc": "299"}, {"messageId": "294", "fix": "361", "desc": "296"}, {"messageId": "297", "fix": "362", "desc": "299"}, {"messageId": "294", "fix": "363", "desc": "296"}, {"messageId": "297", "fix": "364", "desc": "299"}, {"messageId": "294", "fix": "365", "desc": "296"}, {"messageId": "297", "fix": "366", "desc": "299"}, {"messageId": "294", "fix": "367", "desc": "296"}, {"messageId": "297", "fix": "368", "desc": "299"}, {"messageId": "294", "fix": "369", "desc": "296"}, {"messageId": "297", "fix": "370", "desc": "299"}, {"messageId": "294", "fix": "371", "desc": "296"}, {"messageId": "297", "fix": "372", "desc": "299"}, {"messageId": "294", "fix": "373", "desc": "296"}, {"messageId": "297", "fix": "374", "desc": "299"}, {"messageId": "294", "fix": "375", "desc": "296"}, {"messageId": "297", "fix": "376", "desc": "299"}, {"messageId": "294", "fix": "377", "desc": "296"}, {"messageId": "297", "fix": "378", "desc": "299"}, {"messageId": "294", "fix": "379", "desc": "296"}, {"messageId": "297", "fix": "380", "desc": "299"}, {"messageId": "294", "fix": "381", "desc": "296"}, {"messageId": "297", "fix": "382", "desc": "299"}, {"messageId": "294", "fix": "383", "desc": "296"}, {"messageId": "297", "fix": "384", "desc": "299"}, {"messageId": "294", "fix": "385", "desc": "296"}, {"messageId": "297", "fix": "386", "desc": "299"}, {"messageId": "294", "fix": "387", "desc": "296"}, {"messageId": "297", "fix": "388", "desc": "299"}, "suggestUnknown", {"range": "389", "text": "390"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "391", "text": "392"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "393", "text": "390"}, {"range": "394", "text": "392"}, {"range": "395", "text": "390"}, {"range": "396", "text": "392"}, {"range": "397", "text": "390"}, {"range": "398", "text": "392"}, {"range": "399", "text": "390"}, {"range": "400", "text": "392"}, {"range": "401", "text": "390"}, {"range": "402", "text": "392"}, {"range": "403", "text": "390"}, {"range": "404", "text": "392"}, {"range": "405", "text": "390"}, {"range": "406", "text": "392"}, {"range": "407", "text": "390"}, {"range": "408", "text": "392"}, {"range": "409", "text": "390"}, {"range": "410", "text": "392"}, {"range": "411", "text": "390"}, {"range": "412", "text": "392"}, "replaceEmptyInterfaceWithSuper", {"range": "413", "text": "414"}, "Replace empty interface with a type alias.", {"range": "415", "text": "390"}, {"range": "416", "text": "392"}, {"range": "417", "text": "390"}, {"range": "418", "text": "392"}, {"range": "419", "text": "390"}, {"range": "420", "text": "392"}, {"range": "421", "text": "390"}, {"range": "422", "text": "392"}, {"range": "423", "text": "390"}, {"range": "424", "text": "392"}, {"range": "425", "text": "390"}, {"range": "426", "text": "392"}, {"range": "427", "text": "390"}, {"range": "428", "text": "392"}, {"range": "429", "text": "390"}, {"range": "430", "text": "392"}, {"range": "431", "text": "390"}, {"range": "432", "text": "392"}, {"range": "433", "text": "390"}, {"range": "434", "text": "392"}, {"range": "435", "text": "390"}, {"range": "436", "text": "392"}, {"range": "437", "text": "390"}, {"range": "438", "text": "392"}, {"range": "439", "text": "390"}, {"range": "440", "text": "392"}, {"range": "441", "text": "390"}, {"range": "442", "text": "392"}, {"range": "443", "text": "390"}, {"range": "444", "text": "392"}, {"range": "445", "text": "390"}, {"range": "446", "text": "392"}, {"range": "447", "text": "390"}, {"range": "448", "text": "392"}, {"range": "449", "text": "390"}, {"range": "450", "text": "392"}, {"range": "451", "text": "390"}, {"range": "452", "text": "392"}, {"range": "453", "text": "390"}, {"range": "454", "text": "392"}, {"range": "455", "text": "390"}, {"range": "456", "text": "392"}, {"range": "457", "text": "390"}, {"range": "458", "text": "392"}, {"range": "459", "text": "390"}, {"range": "460", "text": "392"}, {"range": "461", "text": "390"}, {"range": "462", "text": "392"}, {"range": "463", "text": "390"}, {"range": "464", "text": "392"}, {"range": "465", "text": "390"}, {"range": "466", "text": "392"}, {"range": "467", "text": "390"}, {"range": "468", "text": "392"}, {"range": "469", "text": "390"}, {"range": "470", "text": "392"}, {"range": "471", "text": "390"}, {"range": "472", "text": "392"}, {"range": "473", "text": "390"}, {"range": "474", "text": "392"}, {"range": "475", "text": "390"}, {"range": "476", "text": "392"}, {"range": "477", "text": "390"}, {"range": "478", "text": "392"}, {"range": "479", "text": "390"}, {"range": "480", "text": "392"}, [1992, 1995], "unknown", [1992, 1995], "never", [2311, 2314], [2311, 2314], [613, 616], [613, 616], [738, 741], [738, 741], [3398, 3401], [3398, 3401], [4979, 4982], [4979, 4982], [6330, 6333], [6330, 6333], [7946, 7949], [7946, 7949], [4190, 4193], [4190, 4193], [5025, 5028], [5025, 5028], [1319, 1322], [1319, 1322], [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1597, 1600], [1597, 1600], [1644, 1647], [1644, 1647], [1885, 1888], [1885, 1888], [1938, 1941], [1938, 1941], [4987, 4990], [4987, 4990], [6257, 6260], [6257, 6260], [10082, 10085], [10082, 10085], [11015, 11018], [11015, 11018], [12366, 12369], [12366, 12369], [12386, 12389], [12386, 12389], [12394, 12397], [12394, 12397], [14004, 14007], [14004, 14007], [16394, 16397], [16394, 16397], [16677, 16680], [16677, 16680], [16993, 16996], [16993, 16996], [17024, 17027], [17024, 17027], [17144, 17147], [17144, 17147], [18180, 18183], [18180, 18183], [18468, 18471], [18468, 18471], [18573, 18576], [18573, 18576], [19330, 19333], [19330, 19333], [19765, 19768], [19765, 19768], [20557, 20560], [20557, 20560], [21133, 21136], [21133, 21136], [22327, 22330], [22327, 22330], [22781, 22784], [22781, 22784], [23351, 23354], [23351, 23354], [23881, 23884], [23881, 23884], [24181, 24184], [24181, 24184], [25907, 25910], [25907, 25910], [26560, 26563], [26560, 26563], [26594, 26597], [26594, 26597], [29744, 29747], [29744, 29747]]