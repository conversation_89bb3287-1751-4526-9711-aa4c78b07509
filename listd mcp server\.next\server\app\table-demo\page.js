(()=>{var e={};e.id=63,e.ids=[63],e.modules={195:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1137:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var s=t(7413),n=t(6797),i=t.n(n);t(1135);var a=t(3701),o=t(5986),d=t(8974);let l={title:"GooSearch - AI 增强的谷歌搜索",description:"使用 AI 技术增强的谷歌搜索引擎，提供更精准的搜索结果分析"};function c({children:e}){return(0,s.jsxs)("html",{lang:"zh-CN",suppressHydrationWarning:!0,children:[(0,s.jsx)("head",{}),(0,s.jsx)("body",{className:function(...e){return(0,d.QP)((0,o.$)(e))}("min-h-screen bg-background font-sans antialiased",i().variable),children:(0,s.jsx)(a.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e})})]})}},2685:(e,r,t)=>{Promise.resolve().then(t.bind(t,6871))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\工作\\Mik\\deep\\listd mcp server\\src\\components\\theme-provider.tsx","ThemeProvider")},3711:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\工作\\\\Mik\\\\deep\\\\listd mcp server\\\\src\\\\app\\\\table-demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\table-demo\\page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},4125:(e,r,t)=>{Promise.resolve().then(t.bind(t,3701))},4413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(7413);function n(){return(0,s.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",background:"linear-gradient(to right, #00d2ff, #3a7bd5)"},children:(0,s.jsxs)("div",{style:{textAlign:"center",color:"white",maxWidth:"600px",padding:"2rem",backgroundColor:"rgba(255, 255, 255, 0.1)",borderRadius:"1rem",backdropFilter:"blur(10px)",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},children:[(0,s.jsx)("h1",{style:{fontSize:"2.5rem",marginBottom:"1rem"},children:"404 - 页面未找到"}),(0,s.jsx)("p",{style:{fontSize:"1.2rem",marginBottom:"2rem"},children:"抱歉，您请求的页面不存在。"}),(0,s.jsx)("a",{href:"/",style:{display:"inline-block",padding:"0.75rem 1.5rem",backgroundColor:"white",color:"#3a7bd5",borderRadius:"0.5rem",textDecoration:"none",fontWeight:"bold",transition:"all 0.2s ease"},children:"返回首页"})]})})}},4493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>a,aR:()=>o,wL:()=>m});var s=t(687),n=t(3210),i=t(4780);let a=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));a.displayName="Card";let o=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let d=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let m=n.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));m.displayName="CardFooter"},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(9384),n=t(2348);function i(...e){return(0,n.QP)((0,s.$)(e))}},5688:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>l});var s=t(5239),n=t(8088),i=t(8170),a=t.n(i),o=t(893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["table-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3711)),"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\table-demo\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1137)),"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4413)),"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\table-demo\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/table-demo/page",pathname:"/table-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},5851:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},6487:()=>{},6871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>i});var s=t(687);t(3210);var n=t(218);function i({children:e,...r}){return(0,s.jsx)(n.N,{...r,children:e})}},7818:(e,r,t)=>{Promise.resolve().then(t.bind(t,9689))},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(687),n=t(3210),i=t(1391),a=t(4224),o=t(4780);let d=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...a},l)=>{let c=n?i.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(d({variant:r,size:t,className:e})),ref:l,...a})});l.displayName="Button"},9666:(e,r,t)=>{Promise.resolve().then(t.bind(t,3711))},9689:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(687),n=t(3210),i=t(1527),a=t(4493),o=t(9523);function d(){let[e,r]=(0,n.useState)(0),t=[{title:"企业信息表格",markdown:`## 与"科技公司"相关的实体信息

| 名称 | 提供方 | 详细内容 | 数据来源 |
| ---- | ------ | -------- | -------- |
| 苹果公司 | Apple Inc. | 全球领先的科技公司，主要产品包括iPhone、iPad、Mac等 | [官方网站](https://www.apple.com) |
| 微软公司 | Microsoft | 软件巨头，Windows操作系统和Office办公套件的开发商 | [官方网站](https://www.microsoft.com) |
| 谷歌公司 | Alphabet Inc. | 搜索引擎和云服务提供商，Android系统开发者 | [官方网站](https://www.google.com) |
| 亚马逊 | Amazon | 电商平台和云计算服务提供商AWS | [官方网站](https://www.amazon.com) |
| 特斯拉 | Tesla Inc. | 电动汽车和清洁能源公司 | [官方网站](https://www.tesla.com) |`},{title:"产品对比表格",markdown:`## 智能手机产品对比

| 产品名称 | 品牌 | 价格 | 屏幕尺寸 | 存储容量 | 评分 |
| -------- | ---- | ---- | -------- | -------- | ---- |
| iPhone 15 Pro | Apple | \xa58999 | 6.1英寸 | 128GB | ⭐⭐⭐⭐⭐ |
| Galaxy S24 Ultra | Samsung | \xa59999 | 6.8英寸 | 256GB | ⭐⭐⭐⭐⭐ |
| Pixel 8 Pro | Google | \xa56999 | 6.7英寸 | 128GB | ⭐⭐⭐⭐ |
| 小米14 Ultra | Xiaomi | \xa55999 | 6.73英寸 | 256GB | ⭐⭐⭐⭐ |
| OnePlus 12 | OnePlus | \xa54999 | 6.82英寸 | 256GB | ⭐⭐⭐⭐ |`},{title:"数据统计表格",markdown:`## 2024年全球市场数据

| 地区 | 人口数量 | GDP总量 | 增长率 | 主要产业 |
| ---- | -------- | ------- | ------ | -------- |
| 中国 | 14.1亿 | $17.7万亿 | ****% | 制造业、科技、服务业 |
| 美国 | 3.3亿 | $26.9万亿 | ****% | 科技、金融、服务业 |
| 日本 | 1.25亿 | $4.9万亿 | +0.9% | 制造业、科技、汽车 |
| 德国 | 8300万 | $4.3万亿 | ****% | 制造业、汽车、化工 |
| 印度 | 14.2亿 | $3.7万亿 | ****% | IT服务、制造业、农业 |`},{title:"复杂数据表格",markdown:`## 全球顶级大学排名

| 排名 | 大学名称 | 国家/地区 | 建校年份 | 学生人数 | 知名校友 | 官方网站 |
| ---- | -------- | --------- | -------- | -------- | -------- | -------- |
| 1 | 哈佛大学 | 美国 | 1636年 | 23,000 | 奥巴马、扎克伯格 | [harvard.edu](https://www.harvard.edu) |
| 2 | 斯坦福大学 | 美国 | 1885年 | 17,000 | 拉里\xb7佩奇、谢尔盖\xb7布林 | [stanford.edu](https://www.stanford.edu) |
| 3 | 麻省理工学院 | 美国 | 1861年 | 11,500 | 钱学森、贝索斯 | [mit.edu](https://www.mit.edu) |
| 4 | 剑桥大学 | 英国 | 1209年 | 24,000 | 牛顿、霍金 | [cam.ac.uk](https://www.cam.ac.uk) |
| 5 | 牛津大学 | 英国 | 1096年 | 26,000 | 撒切尔夫人、托尔金 | [ox.ac.uk](https://www.ox.ac.uk) |`}];return(0,s.jsx)("div",{className:"min-h-screen flex justify-center items-center",style:{background:"linear-gradient(135deg, rgb(var(--mint-start)), rgb(var(--mint-mid)), rgb(var(--mint-end)))",backgroundSize:"200% 200%",animation:"gradient 15s ease infinite"},children:(0,s.jsxs)(a.Zp,{className:"max-w-6xl w-full mx-4 my-8 glass-effect shadow-2xl overflow-hidden z-10 fade-in",children:[(0,s.jsx)("div",{className:"h-1.5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))]"}),(0,s.jsxs)(a.aR,{className:"text-center",children:[(0,s.jsx)(a.ZB,{className:"text-4xl font-bold bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] text-transparent bg-clip-text",children:"表格样式演示"}),(0,s.jsx)("p",{className:"text-lg text-[rgb(var(--text-secondary))] mt-2",children:"展示美化后的表格效果"})]}),(0,s.jsxs)(a.Wu,{className:"p-6 md:p-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)(o.$,{onClick:()=>{r(e=>(e-1+t.length)%t.length)},className:"bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-mid))] text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:scale-105",children:"← 上一个"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-[rgb(var(--text-primary))]",children:t[e].title}),(0,s.jsxs)("p",{className:"text-sm text-[rgb(var(--text-secondary))]",children:[e+1," / ",t.length]})]}),(0,s.jsx)(o.$,{onClick:()=>{r(e=>(e+1)%t.length)},className:"bg-gradient-to-r from-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:scale-105",children:"下一个 →"})]}),(0,s.jsx)("div",{className:"table-container table-success",children:(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:i.xI.parse(t[e].markdown)},className:"prose max-w-none prose-headings:text-center prose-headings:text-[rgb(var(--text-primary))] prose-a:text-[rgb(var(--mint-start))] prose-strong:text-[rgb(var(--mint-start))] enhanced-table"})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--mint-start),0.2)] shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 animate-pulse",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"✨ 表格已成功渲染"]})}),(0,s.jsxs)("div",{className:"mt-8 p-6 bg-[rgba(var(--mint-start),0.05)] border border-[rgba(var(--mint-start),0.2)] rounded-xl",children:[(0,s.jsx)("h3",{className:"text-[rgb(var(--text-primary))] font-semibold mb-4 text-center",children:"✨ 表格美化特性"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-[rgb(var(--text-secondary))]",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:["\uD83C\uDFA8 ",(0,s.jsx)("strong",{children:"渐变表头"}),"：美丽的薄荷色渐变背景"]}),(0,s.jsxs)("p",{children:["\uD83C\uDF0A ",(0,s.jsx)("strong",{children:"悬停效果"}),"：行悬停时的平滑动画"]}),(0,s.jsxs)("p",{children:["\uD83D\uDD17 ",(0,s.jsx)("strong",{children:"链接美化"}),"：带有光泽效果的链接按钮"]}),(0,s.jsxs)("p",{children:["\uD83D\uDCF1 ",(0,s.jsx)("strong",{children:"响应式设计"}),"：完美适配移动设备"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:["✨ ",(0,s.jsx)("strong",{children:"加载动画"}),"：表格行依次出现效果"]}),(0,s.jsxs)("p",{children:["\uD83C\uDFAF ",(0,s.jsx)("strong",{children:"第一列强调"}),"：特殊的边框和背景"]}),(0,s.jsxs)("p",{children:["\uD83C\uDF08 ",(0,s.jsx)("strong",{children:"交替行色"}),"：提高可读性的斑马纹"]}),(0,s.jsxs)("p",{children:["\uD83D\uDCAB ",(0,s.jsx)("strong",{children:"阴影效果"}),"：现代化的立体感设计"]})]})]})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)(o.$,{onClick:()=>window.location.href="/",className:"bg-gradient-to-r from-[rgb(var(--accent-1))] to-[rgb(var(--accent-2))] text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:scale-105",children:"\uD83C\uDFE0 返回主页"})})]})]})})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,130,433],()=>t(5688));module.exports=s})();