(()=>{var e={};e.id=206,e.ids=[206],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},6977:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>f,serverHooks:()=>S,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{POST:()=>g});var s=r(6559),n=r(8088),o=r(7719),i=r(2190),l=r(9021),c=r.n(l),u=r(3873),d=r.n(u);let p=d().join(process.cwd(),"data");async function g(e){let t=new Promise((e,t)=>{setTimeout(()=>t(Error("处理请求超时")),5e3)});try{return await Promise.race([m(e),t])}catch(e){return console.error("保存数据时出错:",e),i.NextResponse.json({error:`保存数据时出错: ${e.message}`},{status:500})}}async function m(e){try{let t,{fileName:r,data:a,append:s=!1,url:n=""}=await e.json();if(!r||!a)return i.NextResponse.json({error:"文件名和数据是必需的"},{status:400});let o=r.replace(/[^a-zA-Z0-9_\-\.]/g,"_"),l=d().join(p,o);c().existsSync(l)||(console.log(`创建新文件 ${o}`),c().writeFileSync(l,"[]"));try{let e=Buffer.from(a).toString("utf8"),r=e.length>500?e.substring(0,500)+"...(更多内容已省略)":e;console.log(`解析JSON数据，长度: ${e.length}, 数据内容预览:
${r}`),t=JSON.parse(e)}catch(e){console.error("解析JSON数据失败:",e),t={raw:a}}let u=new Date().toISOString();if(s){if(c().existsSync(l)&&c().statSync(l).size>0xa00000){let e=`${o.replace(".json","")}_${u.replace(/[:.]/g,"-")}.json`,r=d().join(p,e),a=[{...t,savedAt:u,url:n||void 0}],s=JSON.stringify(a,null,2);return c().writeFileSync(r,s,{encoding:"utf8"}),console.log(`文件 ${o} 太大，已创建新文件 ${e}`),i.NextResponse.json({success:!0,fileName:e,filePath:r.replace(process.cwd(),""),timestamp:u,mode:"new_file",reason:"original_file_too_large"})}let e=[];if(c().existsSync(l))try{let t=c().openSync(l,"r"),r=Buffer.alloc(100);if(c().readSync(t,r,0,100,0),c().closeSync(t),r.toString("utf8").trim().startsWith("[")){let t=c().readFileSync(l,"utf8");try{e=JSON.parse(t),Array.isArray(e)||(e=[e]),e.length>1e3&&(e=e.slice(-1e3),console.log(`文件 ${o} 数据过多，已截断至最新的 1000 条`))}catch(t){console.warn(`无法解析文件 ${o} 中的现有数据，将创建新文件`),e=[]}}else console.warn(`文件 ${o} 不是有效的 JSON 数组，将创建新文件`),e=[]}catch(t){console.warn(`读取文件 ${o} 失败，将创建新文件`),e=[]}if(n){let r=e.findIndex(e=>e.url===n||e.originalLink===n||!!e.metadata&&e.metadata.url===n||!!t.metadata&&!!t.metadata.url&&(e.url===t.metadata.url||e.metadata&&e.metadata.url===t.metadata.url||e.originalLink===t.metadata.url));r>=0?(e[r]={...e[r],...t,updatedAt:u},console.log(`更新文件 ${o} 中URL为 ${n} 的现有数据`)):(e.push({...t,savedAt:u}),console.log(`向文件 ${o} 添加URL为 ${n} 的新数据`))}else if(t.metadata&&t.metadata.url){let r=t.metadata.url,a=e.findIndex(e=>e.url===r||e.originalLink===r||e.metadata&&e.metadata.url===r);a>=0?(e[a]={...e[a],...t,updatedAt:u},console.log(`更新文件 ${o} 中metadata.url为 ${r} 的现有数据`)):(e.push({...t,savedAt:u}),console.log(`向文件 ${o} 添加metadata.url为 ${r} 的新数据`))}else e.push({...t,savedAt:u}),console.log(`向文件 ${o} 添加新数据（无URL）`);let r=JSON.stringify(e,null,2);c().writeFileSync(l,r,{encoding:"utf8"}),console.log(`文件 ${o} 已更新，现有 ${e.length} 条数据`)}else c().writeFileSync(l,a,{encoding:"utf8"}),console.log(`文件 ${o} 已创建/覆盖`);return i.NextResponse.json({success:!0,fileName:o,filePath:l.replace(process.cwd(),""),timestamp:u,mode:s?"append":"overwrite"})}catch(e){throw e}}c().existsSync(p)||c().mkdirSync(p,{recursive:!0});let f=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/save-data/route",pathname:"/api/save-data",filename:"route",bundlePath:"app/api/save-data/route"},resolvedPagePath:"D:\\工作\\Mik\\deep\\listd mcp server\\src\\app\\api\\save-data\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:S}=f;function h(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},8335:()=>{},9021:e=>{"use strict";e.exports=require("fs")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580],()=>r(6977));module.exports=a})();